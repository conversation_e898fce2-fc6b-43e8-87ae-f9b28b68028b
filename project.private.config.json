{"setting": {"compileHotReLoad": true, "urlCheck": false}, "condition": {"miniprogram": {"list": [{"name": "音频模型", "pathName": "pages/SelectVideo/index", "query": "", "launchMode": "default", "scene": null}, {"name": "工作流", "pathName": "pages/WorkflowDetailByTitle/index", "query": "param={}", "launchMode": "default", "scene": null}]}}, "description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "projectname": "BGDAI"}