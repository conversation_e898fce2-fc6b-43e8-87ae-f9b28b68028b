// components/agent-select/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 选中的音频模型信息
    defaultActive: {
      type: Object,
      value: null,
    },
    loading: {
      type: Boolean,
      value: false,
    },
    list: {
      type: Array,
      value: [],
    },
  },
  lifetimes: {
    attached() {
      this.setData({
        active: this.properties.defaultActive,
      });
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    active: {},
  },
  observers: {
    active: function (list) {
      this.triggerEvent("change", list);
    },
  },
  ready() {
    if (this.data.list.length > 0) {
      this.setData({
        active: this.data.list[0],
      });
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {
    onSelect(e) {
      this.setData({
        active: e.currentTarget.dataset.item,
      });
    },
  },
});
