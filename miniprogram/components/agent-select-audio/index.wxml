<view class="selectAudio {{ hasSelected ? 'selected' : '' }}" bindtap="onSelectAudio">
    <block wx:if="{{ !hasSelected }}">
        <van-icon name="music-o" size="36rpx" color="#666"/>
        <view class="select-text">选择音频模型</view>
    </block>
    <block wx:else>
        <view class="audio-content">
            <view class="audio-info">
                <van-icon name="music-o" size="36rpx" color="#FF7F11"/>
                <view class="audio-name">{{ selectedAudio.title }}</view>
            </view>
            <view class="audio-details">
                <view class="audio-duration">时长: {{ selectedAudio.duration }}</view>
                <view class="play-button {{ isPlaying ? 'playing' : '' }}" catchtap="togglePlay" data-action="play">
                    <van-icon name="{{ isPlaying ? 'pause-circle' : 'play-circle' }}" size="40rpx" color="#FF7F11"/>
                </view>
            </view>
            <view class="progress-bar" wx:if="{{ hasSelected }}">
                <view class="progress-fill" style="width: {{ playProgress }}%"></view>
            </view>
        </view>
    </block>
</view>
