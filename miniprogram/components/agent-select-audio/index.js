// components/agent-select-audio/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 选中的音频模型信息
    selectedAudio: {
      type: Object,
      value: null,
      observer: function (newVal) {
        if (newVal && newVal.id) {
          this.setData({
            hasSelected: true,
          });

          // 如果正在播放，停止播放
          if (this.audioContext) {
            this.audioContext.stop();
            this.setData({
              isPlaying: false,
            });
          }
        } else {
          this.setData({
            hasSelected: false,
          });
        }
      },
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    hasSelected: false,
    isPlaying: false,
    playProgress: 0,
  },

  lifetimes: {
    attached() {
      // 组件创建时，初始化状态
      this.setData({
        hasSelected: !!this.properties.selectedAudio.id,
      });
    },

    detached() {
      // 组件销毁时，释放音频资源
      this._destroyAudioContext();
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 点击选择音频模型
     */
    onSelectAudio(e) {
      // 阻止事件冒泡，避免点击播放按钮时触发选择
      if (e.target.dataset.action === "play") {
        return;
      }

      wx.navigateTo({
        url: "/pages/SelectAudio/index",
        events: {
          // 监听选择音频页面返回的数据
          selectAudioCallback: (data) => {
            // 更新选中的音频信息
            this.setData({
              selectedAudio: data,
              hasSelected: true,
              isPlaying: false,
              playProgress: 0,
            });

            // 触发选择事件，通知父组件
            this.triggerEvent("select", data);

            // 如果正在播放，停止播放
            if (this.audioContext) {
              this.audioContext.stop();
            }
          },
        },
        success: (res) => {
          // 向被打开页面传递当前选中的音频信息
          if (this.data.selectedAudio) {
            res.eventChannel.emit("selectedAudioData", {
              audio: this.data.selectedAudio,
            });
          }
        },
      });
    },

    /**
     * 播放/暂停音频
     */
    togglePlay(e) {
      // 阻止事件冒泡，避免触发选择
      // e.stopPropagation();

      if (!this.data.selectedAudio || !this.data.selectedAudio.url) {
        wx.showToast({
          title: "无效的音频源",
          icon: "none",
        });
        return;
      }

      if (this.data.isPlaying) {
        // 暂停播放
        if (this.audioContext) {
          this.audioContext.pause();
          this.setData({
            isPlaying: false,
          });
        }
      } else {
        // 开始播放
        if (!this.audioContext) {
          this._initAudioContext();
        }

        this.audioContext.play();
        this.setData({
          isPlaying: true,
        });
      }
    },

    /**
     * 初始化音频上下文
     */
    _initAudioContext() {
      // 销毁旧的音频上下文
      this._destroyAudioContext();

      // 创建新的音频上下文
      this.audioContext = wx.createInnerAudioContext();
      this.audioContext.src = this.data.selectedAudio.url;

      // 监听音频事件
      this.audioContext.onPlay(() => {
        this.setData({
          isPlaying: true,
        });
      });

      this.audioContext.onPause(() => {
        this.setData({
          isPlaying: false,
        });
      });

      this.audioContext.onStop(() => {
        this.setData({
          isPlaying: false,
          playProgress: 0,
        });
      });

      this.audioContext.onEnded(() => {
        this.setData({
          isPlaying: false,
          playProgress: 0,
        });
      });

      this.audioContext.onError((err) => {
        console.error("Audio error:", err);
        this.setData({
          isPlaying: false,
        });

        wx.showToast({
          title: "音频播放失败",
          icon: "none",
        });
      });

      this.audioContext.onTimeUpdate(() => {
        const currentTime = this.audioContext.currentTime;
        const duration = this.audioContext.duration;

        if (duration > 0) {
          const progress = (currentTime / duration) * 100;
          this.setData({
            playProgress: progress,
          });
        }
      });
    },

    /**
     * 销毁音频上下文
     */
    _destroyAudioContext() {
      if (this.audioContext) {
        this.audioContext.stop();
        this.audioContext.destroy();
        this.audioContext = null;
      }
    },
  },
});
