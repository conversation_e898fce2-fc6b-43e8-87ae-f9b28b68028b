.video_item {
  width: calc(50vw - 36rpx);
  flex-shrink: 1;
  flex-grow: 0;
  background-color: #fff;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  overflow: hidden;
}
.video_content {
  position: relative;
  width: 100%;
  aspect-ratio: 9/10;
  background-color: #e1e1e1;
}
.video_foot {
  padding: 12rpx;
}
.video_content image {
  width: 100%;
  height: 100%;
}
.video_title {
  font-size: 28rpx;
  height: 82rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
.video_time {
  font-size: 28rpx;
  color: #999;
  margin-top: 12rpx;
}
.video_delete {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
}
.play-icon {
  position: absolute;
  width: 52rpx;
  height: 52rpx;
  z-index: 100;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
}
.video-duration {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}
