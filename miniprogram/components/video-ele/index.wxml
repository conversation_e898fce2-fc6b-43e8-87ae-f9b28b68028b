<view class="video_item">
    <view class="video_content">
        <view class="video_delete" bind:tap="deleteVideo" data-id="{{ item.uuid }}" wx:if="{{delete}}">
            <van-icon name="close" color="#fff" size="28px" />
        </view>
        <image src="{{ item.coverUrl || '/assets/video-placeholder.png' }}" mode="aspectFill"></image>
        <view class="video-duration">{{ item.duration }}</view>
        <view class="play-icon" catchtap="playVideo" data-id="{{ item.id }}">
            <van-icon name="play-circle" color="#fff" size="36px" />
        </view>
    </view>
    <view class="video_foot">
        <view class="video_title">{{ item.title }}</view>
        <view class="video_time">{{ item.createTime }}</view>
    </view>
</view>