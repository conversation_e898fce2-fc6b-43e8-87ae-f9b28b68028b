// components/video-ele/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    item: {
      type: Object,
      value: {},
    },
    delete: {
      type: Boolean,
      value: false,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    videoContext: null,
  },
  observers: {
    item: function (item) {
      console.log("item:", item);
    },
  },
  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 播放视频
     */
    playVideo(e) {
      const videoData = this.data.item;
      if (!videoData.videoUrl) {
        wx.showToast({
          title: "视频地址错误",
          icon: "none",
        });
        return;
      }
      // 预览视频
      wx.previewMedia({
        sources: [
          {
            url: videoData.videoUrl,
            type: "video",
          },
        ],
        current: 0,
        success: () => {},
        fail: (err) => {
          console.error("预览视频失败:", err);
          wx.showToast({
            title: "预览视频失败",
            icon: "none",
          });
        },
        complete: () => {
          // 预览结束后重置播放状态
        },
      });
    },
    /** 删除视频 */
    deleteVideo(e) {
      console.log("-----", e);
      const _this = this;
      const id = e.currentTarget.dataset.id;
      wx.showModal({
        title: "提示",
        content: "确认删除当前视频模型吗？删除之后不可恢复！",
        success(res) {
          if (res.confirm) {
            _this.triggerEvent("deleteVideo", id);
          } else if (res.cancel) {
            console.log("用户点击取消");
          }
        },
      });
    },
  },
});
