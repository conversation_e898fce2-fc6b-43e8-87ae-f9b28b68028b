page {
  width: 100%;
  background-color: #f2f2f2;
  --fontSizeDefault: 28rpx;
  --color: #FF7F11;
  --qColor: #f7e2d0;
  --pageBg: #fff;
}

/* page {
  width: 100%;
  background-color: #f2f2f2;
  --fontSizeDefault: 28rpx;
  --color: #FF7F11;
  --qColor: #fce1c9;
  --pageBg: #000;
} */

view,
scroll-view,
page {
  box-sizing: border-box;
}

button {
  background: initial;
}

button:focus {
  outline: 0;
}

button::after {
  border: none;
}

textarea {
  box-sizing: border-box;
}

input {
  box-sizing: border-box;
}