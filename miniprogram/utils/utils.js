/**
 * 将秒数格式化为 MM:SS 形式
 * @param {number} seconds 秒数
 * @returns {string} 格式化后的时间字符串，如 "03:45"
 */
function formatDuration(seconds) {
  if (isNaN(seconds) || seconds < 0) {
    return "00:00";
  }

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  // 补零操作，确保两位数显示
  const paddedMinutes = String(minutes).padStart(2, "0");
  const paddedSeconds = String(remainingSeconds).padStart(2, "0");

  return `${paddedMinutes}:${paddedSeconds}`;
}

module.exports = {
  formatDuration,
};
