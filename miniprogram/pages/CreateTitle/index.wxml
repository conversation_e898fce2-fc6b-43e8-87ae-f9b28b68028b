<view class="container">
  <!-- Header -->
  <view class="header">
    <view class="header-title">创建选题</view>
    <view class="header-subtitle">根据主题信息生成多个选题方向</view>
  </view>

  <!-- Main Content -->
  <view class="content">
    <!-- Input Section -->
    <view class="section">
      <view class="section-header">
        <view class="section-title">
          <text class="required">*</text>
          <text>主题信息</text>
        </view>
        <view class="character-count {{textAreaValue.length > 900 ? 'near-limit' : ''}}">
          {{textAreaValue.length || 0}}/1000
        </view>
      </view>
      <cs-text-area bind:valuechange="textAreaChange" maxValue="1000" height="320" placeholderTxt="请输入要创建选题的内容主题信息" />
    </view>

    <!-- Options Section -->
    <view class="section">
      <view class="section-header">
        <view class="section-title">选题设置</view>
      </view>

      <view class="style-options">
        <view class="style-option-group">
          <view class="option-label">选题数量</view>
          <view class="style-buttons">
            <cs-btn-list list="{{numberList}}" bind:active="activeEvent" defaultValue="{{3}}"></cs-btn-list>
          </view>
        </view>

        <view class="style-option-group">
          <view class="option-label">启用人设</view>
          <van-switch checked="{{ checked }}" active-color="#FF7F11" size="24px" bind:change="switchChangeEve" />
        </view>
      </view>

      <!-- IP Setting Card (only shown when checked is true) -->
      <view class="ip-setting-card" wx:if="{{checked}}">
        <view class="ip-info">
          <view class="ip-title">{{ipSeting.ip_name}}</view>
          <view class="ip-description">{{ipSeting.career_experience}}</view>
        </view>
        <view class="ip-action">
          <button class="change-ip-btn" bind:tap="switchIpSettingEvent">更换</button>
        </view>
      </view>
    </view>
  </view>

  <!-- Footer Actions -->
  <view class="footer">
    <button class="primary-button" bindtap="createSelectTitle" disabled="{{btnState}}">
      创建选题
    </button>
  </view>
</view>