// pages/CreateTitle/index.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    inputData: "",
    textAreaValue: "", // For character count display
    checked: false,
    ipSeting: {
      title: "",
      subTitle: "",
    },
    numberCurrent: 3,
    numberList: [{
        label: "3个",
        value: 3,
      },
      {
        label: "6个",
        value: 6,
      },
      {
        label: "10个",
        value: 10,
      },
    ],
    btnState: true, // 按钮状态
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {},
  /** 监听输入内容变化 */
  textAreaChange(e) {
    const value = e.detail.value;
    this.setData({
      inputData: value,
      textAreaValue: value,
      btnState: value.length > 0 ? false : true,
    });
  },
  /** 监听启用人设变化  */
  switchChangeEve({
    detail
  }) {
    this.setData({
      checked: detail,
    });
  },
  /** 数量选中 */
  activeEvent(e) {
    this.setData({
      numberCurrent: e.detail.value,
    });
  },
  /** 创建选题 */
  createSelectTitle() {
    const data = this.data;
    if (data.inputData.length <= 0) {
      wx.showToast({
        title: "请输入选题内容",
        icon: "none",
      });
      return;
    }
    const param = {
      inputData: data.inputData,
      numberCurrent: data.numberCurrent,
      ipSetting: data.ipSeting,
      type: "createSelectTitle",
    };
    // 判断是否选用人设
    if (data.checked) {
      param.ipSeting = data.ipSeting;
    }
    wx.navigateTo({
      url: "/pages/WorkflowDetailByTitle/index?param=" + JSON.stringify(param),
    });
  },
  /** 选择Ip人设 */
  switchIpSettingEvent() {
    wx.navigateTo({
      url: "/pages/IPListSelect/index",
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    const defaultIpSetting = wx.getStorageSync("selectIpSetting");
    if (defaultIpSetting && defaultIpSetting.checked) {
      const value = defaultIpSetting.checked;
      this.setData({
        ipSeting: value.params,
      });
      delete defaultIpSetting.checked;
      wx.setStorageSync("selectIpSetting", defaultIpSetting);
    } else if (defaultIpSetting && defaultIpSetting.default) {
      const value = defaultIpSetting.default;
      this.setData({
        ipSeting: {
          title: value.ip_name,
          subTitle: value.key_information,
        },
      });
    } else {
      this.setData({
        ipSeting: {
          title: "",
          subTitle: "",
        },
      });
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
});