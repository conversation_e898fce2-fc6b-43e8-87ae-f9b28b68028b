// pages/CreateIPVideo/index.js
const {
  wssData
} = require("../../common/server");
Page({
  /**
   * 页面的初始数据
   */
  data: {
    stepType: "createWriting", // selectTitle:选题 createWriting:文案 selectAudio:选择音频 createAudio:音频 selectVideo:选择视频 createVideo:视频
    btnLoading: false, // 按钮状态
    btnDisabled: false, // 按钮状态
    showLoading: false, // 内容状态
    currentTime: "", // 当前时间
    flowData: {
      selectTitle: {
        status: true,
        params: {},
        data: [
          // {
          //   id: 0,
          //   order_cn: "一",
          //   title: "5个懒人也能坚持的身材管理小妙招",
          //   because:
          //     "现代人生活节奏快，懒人方法更受欢迎。分享简单易行的技巧，如站立办公、碎片化运动等，让观众轻松入门。",
          // },
        ],
        stream: ``,
        currentSelect: {},
        disabled: false, // 是否可选择
      }, // 选题
      createWriting: {
        status: false,
        params: {},
        data: "",
        stream: ``,
        editor: false, // 是否可编辑
        finish: false,
      }, // 创建文案
      selectAudio: {
        status: false,
        data: {},
        disabled: false, // 是否可选择
      }, // 选择音频
      createAudio: {
        status: false,
        modelData: {},
        data: {},
        btnDisabled: true,
        editor: false, // 是否可编辑
      }, // 创建音频
      selectVideo: {
        status: false,
        data: {},
        disabled: false, // 是否可选择
        btnDisabled: true,
      }, // 选择视频
      createVideo: {
        status: false,
        modelData: {},
        data: {},
        editor: false, // 是否可编辑
        btnDisabled: true,
      }, // 创建视频
      complate: {
        status: false,
      },
    },
    currentSelect: {},
    currentSelectAudio: {},
    videoItem: {
      id: "2",
      title: "讲解法律知识视频模型2",
      duration: "03:12",
      coverUrl: "https://img.yzcdn.cn/vant/cat.jpeg",
      videoUrl: "https://wxsnsdy.tc.qq.com/105/20210/snsdyvideodownload?filekey=30280201010421301f0201690402534804102ca905ce620b1241b726bc41dcff44e00204012882540400&bizid=1023&hy=SH&fileparam=302c020101042530230204136ffd93020457e3c4ff02024ef202031e8d7f02030f42400204045a320a0201000400",
      createTime: "2023-05-09 15:45",
    },
    writingTxt: "",
    selectTopicArr: [
      // {
      //   id: 1,
      //   title: "情节严重的是为",
      //   because:
      //     "如果你喜欢火锅和烧烤，那这里绝对是你的天堂。味碟的小料区有20多种佐料，油碟、干碟、海鲜碟应有尽有。",
      // },
    ],
  },

  /**
   * 格式化当前时间
   */
  formatCurrentTime() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");
    const hours = String(now.getHours()).padStart(2, "0");
    const minutes = String(now.getMinutes()).padStart(2, "0");
    const seconds = String(now.getSeconds()).padStart(2, "0");

    return `${year}.${month}.${day} ${hours}:${minutes}:${seconds}`;
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 设置当前时间
    this.setData({
      currentTime: this.formatCurrentTime(),
    });
    let params = {};
    try {
      params = JSON.parse(options.param);
      if (params.type === "createSelectTitle") {
        // this.onCreateCopywriting(params);
      }
    } catch (error) {
      params = {};
    }
  },
  onSelectTopic({
    detail
  }) {
    this.setData({
      [`flowData.selectTitle.currentSelect`]: detail,
    });
  },
  /** 创建选题 */
  onCreateCopywriting(params = {}) {
    const scope = this;
    let selectData = "";
    this.setData({
      [`flowData.selectTitle.status`]: true,
      [`flowData.selectTitle.params`]: params,
      showLoading: true,
      btnLoading: true,
      btnDisabled: true,
      selectTopicArr: [],
    });
    const socket = wssData({
      url: "wssSelectTopic",
    });
    socket.onOpen(() => {
      scope.setData({
        socket: socket,
      });
      socket.send({
        data: JSON.stringify({
          openId: params.openId,
          prompt: params.inputData,
          number: params.numberCurrent,
          ipSetting: params.ipSetting,
          type: "selectTopic",
        }),
        success() {
          // scope.setData({
          //   openId: params.openId,
          //   prompt: params.inputData,
          //   number: params.numberCurrent,
          //   ipSetting: params.ipSetting,
          // });
          console.log("消息发送成功");
        },
        fail(err) {
          scope.setData({
            progress: 3,
          });
          console.error("消息发送失败", err);
        },
      });
      console.log("websocket 已经连接成功");
    });
    socket.onClose(() => {
      scope.setData({
        showLoading: false,
        btnLoading: false,
        btnDisabled: false,
      });
      console.log("websocket 已经关闭连接");
    });
    socket.onMessage((res) => {
      const data = JSON.parse(res.data);
      console.log("data:", data);
      if (data.status === "message") {
        selectData += `${data.message}`;
      } else if (data.status === "finish") {
        let array = JSON.parse(selectData);
        if (Array.isArray(array)) {
          array = array.map((item, index) => {
            return {
              id: index,
              title: item.title,
              because: item.because,
            };
          });
        } else {
          array = [];
        }
        this.setData({
          stepType: "createWriting",
          btnDisabled: false,
          btnLoading: false,
          showLoading: false,
          [`flowData.selectTitle.data`]: array,
          [`flowData.selectTitle.stream`]: selectData,
        });
        socket.close();
      }
    });
  },
  /** 创建文案 */
  onCreateWriting() {
    const scope = this;
    let streamValue = "";
    console.log(
      "scope.data.currentSelect:",
      scope.data.flowData.selectTitle.currentSelect
    );
    // 判断是否有选题
    if (scope.data.flowData.selectTitle.currentSelect.id === undefined) {
      wx.showToast({
        title: "请选择选题",
        icon: "none",
      });
      return;
    }
    this.setData({
      btnLoading: true,
      btnDisabled: true,
      showLoading: true,
      [`flowData.createWriting.status`]: true,
      [`flowData.createWriting.data`]: "",
    });
    // 模拟选题内容
    scope.setData({
      stepType: "selectAudio",
      [`flowData.createWriting.data`]: "以下是一份适合朝九晚五工作、初级健身者的减肥运动计划，兼顾时间灵活性和减脂效率，同时避免过度疲劳。坚持4-8周会看到明显效果。",
      [`flowData.createWriting.stream`]: "以下是一份适合朝九晚五工作、初级健身者的减肥运动计划，兼顾时间灵活性和减脂效率，同时避免过度疲劳。坚持4-8周会看到明显效果。",
      [`flowData.createWriting.finish`]: true,
      showLoading: false,
      btnLoading: false,
      btnDisabled: false,
    });
    return;
    const socket = wssData({
      url: "wssCreateWriting",
    });
    socket.onOpen(() => {
      scope.setData({
        socket: socket,
      });
      socket.send({
        data: JSON.stringify({
          openId: scope.data.openId,
          value: scope.data.flowData.selectTitle.currentSelect.title,
          type: "createWriting",
        }),
        success() {
          console.log("消息发送成功");
        },
        fail(err) {
          scope.setData({
            stepType: "selectTitle",
            showLoading: false,
            btnLoading: false,
            btnDisabled: false,
          });
          console.error("消息发送失败", err);
        },
      });
      console.log("websocket 已经连接成功");
    });
    socket.onClose(() => {
      scope.setData({
        showLoading: false,
        btnLoading: false,
        btnDisabled: false,
      });
      console.log("websocket 已经关闭连接");
    });
    socket.onMessage((res) => {
      const data = JSON.parse(res.data);
      console.log("data:", data);
      if (data.status === "message") {
        streamValue += `${data.message}`;
        scope.setData({
          [`flowData.createWriting.data`]: streamValue,
        });
      } else if (data.status === "finish") {
        scope.setData({
          stepType: "selectAudio",
          [`flowData.createWriting.data`]: streamValue,
          [`flowData.createWriting.stream`]: streamValue,
          [`flowData.createWriting.finish`]: true,
          showLoading: false,
          btnLoading: false,
          btnDisabled: false,
        });
        socket.close();
      }
    });
  },
  /** 选择音频模型 */
  onSelectAudioModel() {
    // 选题不可点击、显示选择音频agent
    this.setData({
      stepType: "createAudio",
      [`flowData.selectAudio.status`]: true,
      [`flowData.selectTitle.disabled`]: true,
      [`flowData.createAudio.btnDisabled`]: true,
    });
  },
  /** 音频模型数据变化 */
  onAudioChange({
    detail
  }) {
    // 创建音频按钮可以点击、获取选择的音频模型
    this.setData({
      stepType: "createAudio",
      [`flowData.selectAudio.status`]: true,
      [`flowData.selectAudio.data`]: detail,
      [`flowData.createAudio.btnDisabled`]: false,
      [`flowData.createAudio.modelData`]: detail,
    });
  },
  /** 合成音频 */
  onCreateAudio() {
    // 显示音频结果agent、选择音频不可点击、文案不可修改、显示选择视频模型按钮
    this.setData({
      stepType: "selectVideo",
      [`flowData.createAudio.status`]: true,
      [`flowData.selectAudio.disabled`]: true,
      [`flowData.createWriting.disabled`]: true,
    });
    // 音频加载出来之后
    setTimeout(() => {
      this.setData({
        [`flowData.selectAudio.disabled`]: true,
        [`flowData.createWriting.disabled`]: true,
      });
    }, 2000);
  },
  /** 选择视频 */
  onSelectVideoModel() {
    // 显示视频选择agent、创建视频这一步
    this.setData({
      stepType: "createVideo",
      [`flowData.selectVideo.status`]: true,
      [`flowData.createVideo.disabled`]: true,
    });
  },
  /** 视频模型数据变化   */
  selectVideoEvent({
    detail
  }) {
    console,
    log("-------");
    this.setData({
      [`flowData.selectVideo.data`]: detail,
      [`flowData.createVideo.btnDisabled`]: false,
      [`flowData.createVideo.modelData`]: detail,
    });
  },
  /** 合成视频 */
  onCreateVideo() {
    // 显示视频结果agent、视频不可点击、音频不可修改
    this.setData({
      stepType: "complete",
      [`flowData.createVideo.status`]: true,
      [`flowData.selectVideo.disabled`]: true,
      [`flowData.createAudio.disabled`]: true,
      [`flowData.selectAudio.disabled`]: true,
    });
  },
  /** 完成 */
  onComplete() {
    this.setData({
      stepType: "complete",
    });
  },
  /** 生命周期函数--监听页面初次渲染完成 */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
});