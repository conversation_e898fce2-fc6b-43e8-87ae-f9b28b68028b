<view class="workflow-container">
  <!-- 工作流进度指示器 -->
  <view class="progress-tracker">
    <view class="progress-step {{stepType === 'selectTitle' || stepType === 'createWriting' || stepType === 'selectAudio' || stepType === 'createAudio' ? 'active' : ''}}">
      <view class="step-indicator">
        <view class="step-icon">
          <view class="step-icon-inner"></view>
        </view>
        <view class="step-label">选题</view>
      </view>
    </view>
    <view class="progress-line {{stepType === 'createWriting' || stepType === 'selectAudio' || stepType === 'createAudio' ? 'active' : ''}}"></view>
    <view class="progress-step {{stepType === 'createWriting' || stepType === 'selectAudio' || stepType === 'createAudio' ? 'active' : ''}}">
      <view class="step-indicator">
        <view class="step-icon">
          <view class="step-icon-inner"></view>
        </view>
        <view class="step-label">文案</view>
      </view>
    </view>
    <view class="progress-line {{stepType === 'selectAudio' || stepType === 'createAudio' ? 'active' : ''}}"></view>
    <view class="progress-step {{stepType === 'selectAudio' || stepType === 'createAudio' ? 'active' : ''}}">
      <view class="step-indicator">
        <view class="step-icon">
          <view class="step-icon-inner"></view>
        </view>
        <view class="step-label">声音</view>
      </view>
    </view>
    <view class="progress-line {{stepType === 'createVideo' ? 'active' : ''}}"></view>
    <view class="progress-step {{stepType === 'createVideo' ? 'active' : ''}}">
      <view class="step-indicator">
        <view class="step-icon">
          <view class="step-icon-inner"></view>
        </view>
        <view class="step-label">视频</view>
      </view>
    </view>
  </view>
  <!-- 主内容区域 -->
  <view class="content-area">
    <!-- 选题区块 -->
    <view class="content-block {{flowData.selectTitle.status ? 'active' : ''}}" wx:if="{{flowData.selectTitle.status}}">
      <view class="block-header">
        <view class="avatar-container">
          <image class="avatar" src="/assets/images/ai-avatar.png" mode="aspectFill" />
          <view class="avatar-badge"></view>
        </view>
        <view class="header-info">
          <view class="header-title">创作的选题</view>
          <view class="header-time">{{currentTime || '2025.06.07 12:12:01'}}</view>
        </view>
      </view>
      <view class="block-content {{flowData.selectTitle.disabled?'disabled':''}}">
        <sc-agent-select defaultActive="{{flowData.selectTitle.currentSelect}}" loading="{{flowData.selectTitle.data.length <= 0}}" list="{{flowData.selectTitle.data}}" bind:change="onSelectTopic"></sc-agent-select>
      </view>
    </view>

    <!-- 文案区块 -->
    <view class="content-block {{flowData.createWriting.status ? 'active' : ''}}" wx:if="{{flowData.createWriting.status}}">
      <view class="block-header">
        <view class="avatar-container">
          <image class="avatar" src="/assets/images/ai-avatar.png" mode="aspectFill" />
          <view class="avatar-badge"></view>
        </view>
        <view class="header-info">
          <view class="header-title">创作的文案</view>
          <view class="header-time">{{currentTime || '2025.06.07 12:12:01'}}</view>
        </view>
        <view class="writing-editor" wx:if="{{stepType === 'selectAudio' || stepType === 'createAudio'}}">
          <van-icon name="edit" size="30" color="#ff7f11" />
        </view>
      </view>
      <view class="block-content {{flowData.createWriting.disabled?'disabled':''}}">
        <view class="loading-container" wx:if="{{flowData.createWriting.data.length <= 0}}">
          <van-loading color="#FF7F11" type="spinner" size="36px" />
          <view class="loading-text">AI正在创作中...</view>
        </view>
        <view class="writing-content" wx:else>
          {{flowData.createWriting.data}}
        </view>
      </view>
    </view>

    <!-- 音频选择区块 -->
    <view class="content-block {{flowData.selectAudio.status ? 'active' : ''}}" wx:if="{{flowData.selectAudio.status}}">
      <view class="block-header">
        <view class="avatar-container">
          <image class="avatar" src="/assets/images/ai-avatar.png" mode="aspectFill" />
          <view class="avatar-badge"></view>
        </view>
        <view class="header-info">
          <view class="header-title">选择克隆的音频</view>
          <view class="header-time">{{currentTime || '2025.06.07 12:12:01'}}</view>
        </view>
      </view>
      <view class="block-content  {{flowData.selectAudio.disabled?'disabled':''}}">
        <sc-agent-select-audio bind:select="onAudioChange" selectedAudio="{{flowData.selectAudio.data}}"></sc-agent-select-audio>
      </view>
    </view>

    <!-- 音频结果区块 -->
    <view class="content-block {{flowData.createAudio.status ? 'active' : ''}}" wx:if="{{flowData.createAudio.status}}">
      <view class="block-header">
        <view class="avatar-container">
          <image class="avatar" src="/assets/images/ai-avatar.png" mode="aspectFill" />
          <view class="avatar-badge"></view>
        </view>
        <view class="header-info">
          <view class="header-title">音频结果</view>
          <view class="header-time">{{currentTime || '2025.06.07 12:12:01'}}</view>
        </view>
      </view>
      <view class="block-content audio-content">
        <sc-agent-audio src="/assets/test.m4a"></sc-agent-audio>
      </view>
    </view>

    <!-- 视频选择区块 -->
    <view class="content-block {{flowData.selectVideo.status ? 'active' : ''}}" wx:if="{{flowData.selectVideo.status}}">
      <view class="block-header">
        <view class="avatar-container">
          <image class="avatar" src="/assets/images/ai-avatar.png" mode="aspectFill" />
          <view class="avatar-badge"></view>
        </view>
        <view class="header-info">
          <view class="header-title">选择视频模型</view>
          <view class="header-time">{{currentTime || '2025.06.07 12:12:01'}}</view>
        </view>
      </view>
      <view class="block-content">
        <sc-agent-select-video bind:select="{{selectVideoEvent}}"></sc-agent-select-video>
      </view>
    </view>

    <!-- 视频结果区块 -->
    <view class="content-block {{flowData.createVideo.status ? 'active' : ''}}" wx:if="{{flowData.createVideo.status}}">
      <view class="block-header">
        <view class="avatar-container">
          <image class="avatar" src="/assets/images/ai-avatar.png" mode="aspectFill" />
          <view class="avatar-badge"></view>
        </view>
        <view class="header-info">
          <view class="header-title">视频结果</view>
          <view class="header-time">{{currentTime || '2025.06.07 12:12:01'}}</view>
        </view>
      </view>
      <view class="block-content video-content">
        <sc-video-ele item="{{videoItem}}"></sc-video-ele>
      </view>
    </view>
  </view>
  <!-- 底部操作区域 -->
  <view class="action-footer">
    <!-- 选题阶段 -->
    <view wx:if="{{stepType === 'selectTitle'}}" class="action-buttons">
      <button class="action-btn primary-btn" bind:tap="onCreateCopywriting" loading="{{btnLoading}}" disabled="{{btnDisabled}}">
        <view class="btn-content">
          <view class="btn-icon generate-icon" wx:if="{{!btnLoading}}"></view>
          <text>生成选题</text>
        </view>
      </button>
    </view>

    <!-- 创建文案阶段 -->
    <view wx:if="{{stepType === 'createWriting'}}" class="action-buttons">
      <button class="action-btn primary-btn" bind:tap="onCreateWriting" loading="{{btnLoading}}" disabled="{{btnDisabled}}">
        <view class="btn-content">
          <view class="btn-icon writing-icon" wx:if="{{!btnLoading}}"></view>
          <text>创建文案</text>
        </view>
      </button>
    </view>

    <!-- 选择生成音频阶段 -->
    <view wx:if="{{stepType === 'selectAudio'}}" class="action-buttons">
      <button class="action-btn secondary-btn" bind:tap="onCreateWriting" loading="{{btnLoading}}" disabled="{{btnDisabled}}">
        <view class="btn-content">
          <view class="btn-icon refresh-icon" wx:if="{{!btnLoading}}"></view>
          <text>重新生成文案</text>
        </view>
      </button>
      <button class="action-btn primary-btn" bind:tap="onSelectAudioModel" disabled="{{btnDisabled}}">
        <view class="btn-content">
          <view class="btn-icon audio-icon"></view>
          <text>选择音频模型</text>
        </view>
      </button>
    </view>

    <!-- 创建音频阶段 -->
    <view wx:if="{{stepType === 'createAudio'}}" class="action-buttons">
      <button class="action-btn secondary-btn" bind:tap="onCreateWriting" loading="{{btnLoading}}" disabled="{{btnDisabled}}">
        <view class="btn-content">
          <view class="btn-icon refresh-icon" wx:if="{{!btnLoading}}"></view>
          <text>重新生成文案</text>
        </view>
      </button>
      <button class="action-btn primary-btn" bind:tap="onCreateAudio" disabled="{{flowData.createAudio.btnDisabled}}">
        <view class="btn-content">
          <view class="btn-icon create-icon"></view>
          <text>创建音频</text>
        </view>
      </button>
    </view>
    <!-- 选择视频模型 -->
    <view wx:if="{{stepType === 'selectVideo'}}" class="action-buttons">
      <button class="action-btn secondary-btn" bind:tap="onCreateAudio" loading="{{btnLoading}}" disabled="{{btnDisabled}}">
        <view class="btn-content">
          <view class="btn-icon refresh-icon" wx:if="{{!btnLoading}}"></view>
          <text>重新合成音频</text>
        </view>
      </button>
      <button class="action-btn primary-btn" bind:tap="onSelectVideoModel" disabled="{{btnDisabled}}">
        <view class="btn-content">
          <view class="btn-icon video-icon"></view>
          <text>选择视频模型</text>
        </view>
      </button>
    </view>

    <!-- 生成视频阶段 -->
    <view wx:if="{{stepType === 'createVideo'}}" class="action-buttons">
      <button class="action-btn secondary-btn">
        <view class="btn-content">
          <view class="btn-icon refresh-icon"></view>
          <text>重新生成音频</text>
        </view>
      </button>
      <button class="action-btn primary-btn" disabled="{{flowData.createVideo.btnDisabled}}" bind:tap="onCreateVideo">
        <view class="btn-content">
          <view class="btn-icon complete-icon"></view>
          <text>合成视频</text>
        </view>
      </button>
    </view>

    <!-- 完成阶段 -->
    <view wx:if="{{stepType === 'complete'}}" class="action-buttons">
      
      <button class="action-btn primary-btn" bind:tap="onCreateVideo">
        <view class="btn-content">
          <view class="btn-icon complete-icon"></view>
          <text>重新合成视频</text>
        </view>
      </button>
      <button class="action-btn primary-btn" bind:tap="onComplete">
        <view class="btn-content">
          <view class="btn-icon complete-icon"></view>
          <text>完成</text>
        </view>
      </button>
    </view>
  </view>

</view>