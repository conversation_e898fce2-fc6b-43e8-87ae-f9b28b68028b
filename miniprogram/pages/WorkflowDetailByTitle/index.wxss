/* pages/WorkflowDetailByTitle/index.wxss */
page {
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  padding-bottom: 180rpx;
  /* Space for fixed footer */
}

/* Container styling */
.workflow-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Progress tracker styling */
.progress-tracker {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx 24rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}

.step-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.step-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.step-icon-inner {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #ccc;
  transition: all 0.3s ease;
}

.step-label {
  font-size: 24rpx;
  color: #999;
  transition: all 0.3s ease;
}

.progress-line {
  flex: 1;
  height: 2rpx;
  background-color: #f0f0f0;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

/* Active step styling */
.progress-step.active .step-icon {
  background-color: rgba(255, 127, 17, 0.1);
}

.progress-step.active .step-icon-inner {
  background-color: #ff7f11;
}

.progress-step.active .step-label {
  color: #333;
  font-weight: 500;
}

.progress-line.active {
  background-color: #ff7f11;
}

/* Content area styling */
.content-area {
  flex: 1;
  padding: 24rpx;
}

.content-block {
  margin-bottom: 32rpx;
  padding: 24rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  transform: translateY(20rpx);
  opacity: 0.7;
}

.content-block.active {
  transform: translateY(0);
  opacity: 1;
}

.block-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.avatar-container {
  position: relative;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #f0f0f0;
  border: 2rpx solid #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.avatar-badge {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #ff7f11;
  border: 2rpx solid #fff;
}

.header-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.writing-editor {
  margin-left: auto;
}

.header-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 6rpx;
}

.header-time {
  font-size: 24rpx;
  color: #999;
}

.block-content {
  padding: 0 0 0 96rpx;
}

.block-content.disabled {
  pointer-events: none;
}

/* Loading container styling */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  gap: 24rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* Writing content styling */
.writing-content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap;
}

/* Audio and video content styling */
.audio-content,
.video-content {
  padding: 16rpx 0 0 96rpx;
}

/* Footer styling */
.action-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 32rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  display: flex;
  justify-content: center;
}

.action-buttons {
  display: flex;
  gap: 24rpx;
  width: 100%;
  max-width: 750rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  padding: 0;
  margin: 0;
  transition: all 0.3s ease;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.btn-icon {
  width: 32rpx;
  height: 32rpx;
  position: relative;
}

.primary-btn {
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 127, 17, 0.2);
}

.primary-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(255, 127, 17, 0.3);
}

.secondary-btn {
  background-color: #f5f5f5;
  color: #666;
}

.secondary-btn:active {
  transform: scale(0.98);
  background-color: #ebebeb;
}

/* Button icons */
.generate-icon::before,
.generate-icon::after {
  content: "";
  position: absolute;
  background-color: #fff;
}

.generate-icon::before {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  top: 8rpx;
  left: 8rpx;
}

.generate-icon::after {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  top: 4rpx;
  left: 20rpx;
  opacity: 0.7;
}

.writing-icon::before,
.writing-icon::after {
  content: "";
  position: absolute;
  background-color: #fff;
}

.writing-icon::before {
  width: 20rpx;
  height: 2rpx;
  top: 10rpx;
  left: 6rpx;
}

.writing-icon::after {
  width: 20rpx;
  height: 2rpx;
  top: 20rpx;
  left: 6rpx;
}

.refresh-icon {
  border: 2rpx solid #666;
  border-radius: 50%;
  width: 20rpx;
  height: 20rpx;
  top: 6rpx;
  left: 6rpx;
}

.refresh-icon::before {
  content: "";
  position: absolute;
  width: 6rpx;
  height: 6rpx;
  border-top: 2rpx solid #666;
  border-right: 2rpx solid #666;
  top: -4rpx;
  right: 4rpx;
  transform: rotate(45deg);
}

.audio-icon::before,
.audio-icon::after {
  content: "";
  position: absolute;
  background-color: #fff;
}

.audio-icon::before {
  width: 4rpx;
  height: 16rpx;
  top: 8rpx;
  left: 10rpx;
  border-radius: 2rpx;
}

.audio-icon::after {
  width: 4rpx;
  height: 24rpx;
  top: 4rpx;
  left: 18rpx;
  border-radius: 2rpx;
}

.create-icon::before {
  content: "";
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid #fff;
  border-radius: 50%;
  top: 5rpx;
  left: 5rpx;
}

.complete-icon::before,
.complete-icon::after {
  content: "";
  position: absolute;
  background-color: #fff;
}

.complete-icon::before {
  width: 6rpx;
  height: 12rpx;
  transform: rotate(45deg);
  top: 12rpx;
  left: 16rpx;
}

.complete-icon::after {
  width: 16rpx;
  height: 6rpx;
  transform: rotate(45deg);
  top: 16rpx;
  left: 8rpx;
}