<view class="container">
  <!-- Header Section -->
  <view class="header">
    <view class="title">音频合成</view>
    <view class="subtitle">将文本转换为自然流畅的语音</view>
  </view>

  <!-- Main Content -->
  <view class="content">
    <!-- Text Input Section -->
    <view class="section">
      <view class="section-title">
        <text class="required">*</text>
        <text>输入文本</text>
        <text class="character-count">{{ textLength }}/500</text>
      </view>
      <view class="text-area-container {{ errors.text ? 'error' : '' }}">
        <textarea class="text-input" placeholder="请输入需要转换为语音的文本内容..." bindinput="onTextInput" value="{{ text }}" maxlength="500" auto-height></textarea>
      </view>
      <view class="error-message" wx:if="{{ errors.text }}">{{ errors.text }}</view>

      <!-- Quick Actions -->
      <view class="quick-actions">
        <view class="action-button" bindtap="clearText">
          <van-icon name="delete-o" size="16px" />
          <text>清空</text>
        </view>
        <view class="action-button" bindtap="pasteText">
          <van-icon name="description" size="16px" />
          <text>粘贴</text>
        </view>
        <view class="action-button" bindtap="useTemplate">
          <van-icon name="label-o" size="16px" />
          <text>模板</text>
        </view>
      </view>
    </view>

    <!-- Voice Selection Section -->
    <view class="section">
      <view class="section-title">
        <text class="required">*</text>
        <text>选择音频模型</text>
      </view>
      <view class="voice-selection">
        <agent-select-audio selectedAudio="{{ selectedAudio }}" bind:select="onSelectAudio" />
      </view>
      <view class="error-message" wx:if="{{ errors.audio }}">{{ errors.audio }}</view>
    </view>

    <!-- Audio Settings Section -->
    <view class="section">
      <view class="section-title">
        <text>音频设置</text>
      </view>

      <!-- Speed Setting -->
      <view class="setting-item">
        <view class="setting-label">
          <text>语速</text>
          <text class="setting-value">{{ speed }}</text>
        </view>
        <view class="slider-container">
          <slider min="0.5" max="2.0" step="0.25" value="{{ speed }}" activeColor="#FF7F11" block-size="24" bindchange="onSpeedChange" />
          <view class="slider-labels">
            <text>慢</text>
            <text>快</text>
          </view>
        </view>
      </view>

      <!-- Pitch Setting -->
      <!-- <view class="setting-item">
        <view class="setting-label">
          <text>音调</text>
          <text class="setting-value">{{ pitch }}</text>
        </view>
        <view class="slider-container">
          <slider min="0.5" max="2.0" step="0.1" value="{{ pitch }}" activeColor="#FF7F11" block-size="24" bindchange="onPitchChange" />
          <view class="slider-labels">
            <text>低</text>
            <text>高</text>
          </view>
        </view>
      </view> -->

      <!-- Volume Setting -->
      <!-- <view class="setting-item">
        <view class="setting-label">
          <text>音量</text>
          <text class="setting-value">{{ volume }}</text>
        </view>
        <view class="slider-container">
          <slider min="0" max="1.0" step="0.1" value="{{ volume }}" activeColor="#FF7F11" block-size="24" bindchange="onVolumeChange" />
          <view class="slider-labels">
            <text>小</text>
            <text>大</text>
          </view>
        </view>
      </view> -->
    </view>

    <!-- Preview Section (shows after generation) -->
    <view class="section" wx:if="{{ audioUrl }}">
      <view class="section-title">
        <text>预览</text>
      </view>
      <view class="audio-preview">
        <agent-audio title="生成的音频" src="{{ audioUrl }}" fileSize="{{ fileSize }}" bind:download="onAudioDownload" />
      </view>
    </view>
  </view>

  <!-- Bottom Action Bar -->
  <view class="action-bar">
    <button class="action-button secondary" bindtap="resetForm" disabled="{{ isGenerating }}">
      重置
    </button>
    <button class="action-button primary" bindtap="generateAudio" loading="{{ isGenerating }}" disabled="{{ !canGenerate }}">
      {{ isGenerating ? '生成中...' : '生成音频' }}
    </button>
  </view>

  <!-- Template Selection Dialog -->
  <van-popup show="{{ showTemplates }}" round position="bottom" custom-style="height: 60%;" bind:close="closeTemplates">
    <view class="template-container">
      <view class="template-header">
        <view class="template-title">选择模板</view>
        <view class="template-close" bindtap="closeTemplates">
          <van-icon name="cross" />
        </view>
      </view>
      <view class="template-list">
        <view class="template-item" wx:for="{{ templates }}" wx:key="id" bindtap="selectTemplate" data-id="{{ item.id }}">
          <view class="template-item-title">{{ item.title }}</view>
          <view class="template-item-preview">{{ item.preview }}</view>
        </view>
      </view>
    </view>
  </van-popup>
</view>