// pages/CreateAudio/index.js
const app = getApp();

const {
  PostData
} = require("../../common/server");

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 文本输入
    text: "",
    textLength: 0,

    // 音频模型
    selectedAudio: {},

    // 音频设置
    speed: 1.0,
    pitch: 1.0,
    volume: 1.0,

    // 生成的音频
    audioUrl: "",
    fileSize: 0,

    // 状态控制
    isGenerating: false,
    canGenerate: false,
    errors: {},

    // 模板选择
    showTemplates: false,
    templates: [{
        id: "1",
        title: "产品介绍",
        preview: "我们的产品采用先进技术，为用户提供最佳体验...",
        content: "我们的产品采用先进技术，为用户提供最佳体验。它不仅性能卓越，而且操作简单，是您日常生活的得力助手。无论是工作还是娱乐，它都能满足您的各种需求。",
      },
      {
        id: "2",
        title: "活动通知",
        preview: "诚邀您参加我们的年度活动，届时将有精彩内容...",
        content: "诚邀您参加我们的年度活动，届时将有精彩内容与您分享。活动将于下周六下午2点在市中心举行，请提前安排好您的时间。我们准备了丰富的礼品和精彩的表演，期待您的到来！",
      },
      {
        id: "3",
        title: "教学指导",
        preview: "按照以下步骤操作，您将轻松完成任务...",
        content: '按照以下步骤操作，您将轻松完成任务。首先，打开应用并登录您的账户。然后，点击主页面上的"创建"按钮。接下来，选择您需要的模板并进行个性化设置。最后，点击"保存"按钮完成操作。如有任何问题，请随时联系我们的客服团队。',
      },
    ],
  },

  /**
   * 文本输入事件
   */
  onTextInput(e) {
    const text = e.detail.value;
    const textLength = text.length;

    // 更新文本和字数
    this.setData({
      text,
      textLength,
      "errors.text": "",
    });

    // 验证表单
    this.validateForm();
  },

  /**
   * 清空文本
   */
  clearText() {
    this.setData({
      text: "",
      textLength: 0,
    });

    // 验证表单
    this.validateForm();
  },

  /**
   * 粘贴文本
   */
  pasteText() {
    wx.getClipboardData({
      success: (res) => {
        if (res.data) {
          // 如果粘贴的内容超过500字符，截取前500个字符
          const text = res.data.slice(0, 500);
          const textLength = text.length;

          this.setData({
            text,
            textLength,
            "errors.text": "",
          });

          // 验证表单
          this.validateForm();

          wx.showToast({
            title: "已粘贴",
            icon: "success",
          });
        }
      },
    });
  },

  /**
   * 打开模板选择
   */
  useTemplate() {
    this.setData({
      showTemplates: true,
    });
  },

  /**
   * 关闭模板选择
   */
  closeTemplates() {
    this.setData({
      showTemplates: false,
    });
  },

  /**
   * 选择模板
   */
  selectTemplate(e) {
    const id = e.currentTarget.dataset.id;
    const template = this.data.templates.find((item) => item.id === id);

    if (template) {
      this.setData({
        text: template.content,
        textLength: template.content.length,
        showTemplates: false,
        "errors.text": "",
      });

      // 验证表单
      this.validateForm();
    }
  },

  /**
   * 选择音频模型
   */
  onSelectAudio(e) {
    this.setData({
      selectedAudio: e.detail,
      "errors.audio": "",
    });

    // 验证表单
    this.validateForm();
  },

  /**
   * 语速变化
   */
  onSpeedChange(e) {
    this.setData({
      speed: e.detail.value,
    });
  },

  /**
   * 音调变化
   */
  onPitchChange(e) {
    this.setData({
      pitch: e.detail.value,
    });
  },

  /**
   * 音量变化
   */
  onVolumeChange(e) {
    this.setData({
      volume: e.detail.value,
    });
  },

  /**
   * 验证表单
   */
  validateForm() {
    const {
      text,
      selectedAudio
    } = this.data;
    const errors = {};

    // 验证文本
    if (!text || text.trim() === "") {
      errors.text = "请输入要转换的文本";
    }

    // 验证音频模型
    if (!selectedAudio) {
      errors.audio = "请选择音频模型";
    }

    // 更新错误和生成按钮状态
    this.setData({
      errors,
      canGenerate: Object.keys(errors).length === 0,
    });

    return Object.keys(errors).length === 0;
  },

  /**
   * 生成音频
   */
  generateAudio() {
    // 验证表单
    if (!this.validateForm()) {
      wx.showToast({
        title: "请完善必填信息",
        icon: "none",
      });
      return;
    }

    // 设置生成中状态
    this.setData({
      isGenerating: true,
    });

    const params = {
      text: this.data.text,
      title: this.data.selectedAudio.title,
      speed: this.data.speed,
    };
    PostData({
      url: "SingleTexToAudio",
      params,
    }).then((res) => {
      console.log("res:", res);
      this.setData({
        isGenerating: false,
        audioUrl: res.data.url,
        // fileSize: res.data.size,
        duration: res.data.duration,
        uuid: res.data.uuid,
      });

      wx.showToast({
        title: "生成成功",
        icon: "success",
      });

      // 滚动到预览区域
      wx.createSelectorQuery()
        .select(".audio-preview")
        .boundingClientRect((rect) => {
          wx.pageScrollTo({
            scrollTop: rect.top,
            duration: 300,
          });
        })
        .exec();
    });
    // // 模拟生成过程
    // setTimeout(() => {
    //   // 模拟生成结果
    //   this.setData({
    //     isGenerating: false,
    //     audioUrl:
    //       "https://storage.googleapis.com/media-session/sintel/snow-fight.mp3",
    //     fileSize: 1024 * 1024 * 2.5, // 2.5 MB
    //   });

    //   wx.showToast({
    //     title: "生成成功",
    //     icon: "success",
    //   });

    //   // 滚动到预览区域
    //   wx.createSelectorQuery()
    //     .select(".audio-preview")
    //     .boundingClientRect((rect) => {
    //       wx.pageScrollTo({
    //         scrollTop: rect.top,
    //         duration: 300,
    //       });
    //     })
    //     .exec();
    // }, 2000);
  },

  /**
   * 重置表单
   */
  resetForm() {
    wx.showModal({
      title: "确认重置",
      content: "确定要重置所有内容吗？",
      success: (res) => {
        if (res.confirm) {
          this.setData({
            text: "",
            textLength: 0,
            selectedAudio: null,
            speed: 1.0,
            pitch: 1.0,
            volume: 1.0,
            audioUrl: "",
            fileSize: 0,
            errors: {},
            canGenerate: false,
          });

          wx.showToast({
            title: "已重置",
            icon: "success",
          });
        }
      },
    });
  },

  /**
   * 音频下载成功回调
   */
  onAudioDownload(e) {
    console.log("音频下载成功:", e.detail.savedFilePath);
    wx.showToast({
      title: "下载成功",
      icon: "success",
    });
  },

  /**
   * 播放速度变化事件
   */
  onPlaybackSpeedChange(e) {
    const rate = e.detail.rate;
    console.log("播放速度变化:", rate);

    // 可以在这里保存用户的播放速度偏好
    try {
      wx.setStorageSync("preferredPlaybackRate", rate);
    } catch (error) {
      console.log("保存播放速度偏好失败:", error);
    }

    // 显示速度变化提示
    wx.showToast({
      title: `播放速度: ${rate}x`,
      icon: "none",
      duration: 1000,
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 如果有传入的文本参数，则设置到文本框
    if (options.text) {
      const text = decodeURIComponent(options.text);
      this.setData({
        text,
        textLength: text.length,
      });

      // 验证表单
      this.validateForm();
    }
  },
});