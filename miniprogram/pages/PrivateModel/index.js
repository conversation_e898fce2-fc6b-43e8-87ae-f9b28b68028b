// pages/PrivateModel/index.js

const { getUrl, PostData, GetData } = require("../../common/server");
Page({
  /**
   * 页面的初始数据
   */
  data: {
    activeTab: "video",
    selectedVideoId: "",
    selectedAudioId: "",
    videoList: [
      // {
      //   id: "1",
      //   duration: "23:10",
      //   title: "坐着的安静-讲解法律知识妻女呢",
      //   createTime: "2025-04-09 12:03:01",
      //   coverUrl: "https://img.yzcdn.cn/vant/cat.jpeg",
      //   videoUrl:
      //     "https://wxsnsdy.tc.qq.com/105/20210/snsdyvideodownload?filekey=30280201010421301f0201690402534804102ca905ce620b1241b726bc41dcff44e00204012882540400&bizid=1023&hy=SH&fileparam=302c020101042530230204136ffd93020457e3c4ff02024ef202031e8d7f02030f42400204045a320a0201000400",
      // },
      // {
      //   id: "2",
      //   duration: "10:10",
      //   title: "坐着的安静-讲解法律知识成绩都是你",
      //   createTime: "2025-04-09 12:03:01",
      //   coverUrl: "https://img.yzcdn.cn/vant/cat.jpeg",
      //   videoUrl:
      //     "https://wxsnsdy.tc.qq.com/105/20210/snsdyvideodownload?filekey=30280201010421301f0201690402534804102ca905ce620b1241b726bc41dcff44e00204012882540400&bizid=1023&hy=SH&fileparam=302c020101042530230204136ffd93020457e3c4ff02024ef202031e8d7f02030f42400204045a320a0201000400",
      // },
      // {
      //   id: "3",
      //   duration: "9:10",
      //   title: "坐着的安静-讲解法律知识积极哦的风景",
      //   createTime: "2025-04-09 12:03:01",
      //   coverUrl: "https://img.yzcdn.cn/vant/cat.jpeg",
      //   videoUrl:
      //     "https://wxsnsdy.tc.qq.com/105/20210/snsdyvideodownload?filekey=30280201010421301f0201690402534804102ca905ce620b1241b726bc41dcff44e00204012882540400&bizid=1023&hy=SH&fileparam=302c020101042530230204136ffd93020457e3c4ff02024ef202031e8d7f02030f42400204045a320a0201000400",
      // },
    ],
    audioList: [
      // {
      //   id: "a1",
      //   duration: "02:45",
      //   title: "专业女声解说",
      //   createTime: "2025-04-10 15:30:22",
      //   coverUrl: "/assets/images/audio-cover-1.png",
      //   audioUrl: "https://example.com/audio1.mp3",
      //   type: "female",
      //   description: "适合产品介绍、教程讲解等场景",
      // },
      // {
      //   id: "a2",
      //   duration: "01:58",
      //   title: "男声商业配音",
      //   createTime: "2025-04-09 09:15:40",
      //   coverUrl: "/assets/images/audio-cover-2.png",
      //   audioUrl: "https://example.com/audio2.mp3",
      //   type: "male",
      //   description: "适合商业广告、企业宣传片等场景",
      // },
      // {
      //   id: "a3",
      //   duration: "03:12",
      //   title: "童声配音模型",
      //   createTime: "2025-04-08 14:22:10",
      //   coverUrl: "/assets/images/audio-cover-3.png",
      //   audioUrl: "https://example.com/audio3.mp3",
      //   type: "child",
      //   description: "适合儿童内容、卡通角色等场景",
      // },
      // {
      //   id: "a4",
      //   duration: "02:30",
      //   title: "情感女声模型",
      //   createTime: "2025-04-07 11:05:33",
      //   coverUrl: "/assets/images/audio-cover-4.png",
      //   audioUrl: "https://example.com/audio4.mp3",
      //   type: "female",
      //   description: "适合情感内容、故事叙述等场景",
      // },
    ],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 如果有tab参数，切换到对应的标签页
    if (options && options.tab) {
      this.setData({
        activeTab: options.tab,
      });
    }
  },

  /**
   * 选择视频
   */
  selectVideo(e) {
    const index = e.currentTarget.dataset.index;
    const video = this.data.videoList[index];

    // 获取页面事件通道
    const eventChannel = this.getOpenerEventChannel();

    // 将选中的视频数据传回上一页
    if (eventChannel) {
      eventChannel.emit("selectVideoCallback", video);
    }
    wx.navigateBack();
  },

  /**
   * 选择音频
   */
  selectAudio(e) {
    const index = e.currentTarget.dataset.index;
    const audio = this.data.audioList[index];

    // 获取页面事件通道
    const eventChannel = this.getOpenerEventChannel();

    // 将选中的音频数据传回上一页
    if (eventChannel) {
      eventChannel.emit("selectAudioCallback", audio);
    }
    wx.navigateBack();
  },

  /**
   * 添加视频模型
   */
  addVideoModel() {
    // wx.showToast({
    //   title: "添加视频模型功能即将上线",
    //   icon: "none",
    //   duration: 2000,
    // });

    // 实际应用中可以跳转到上传视频模型的页面
    wx.navigateTo({
      url: "/pages/AddMedia/index?type=video",
    });
    return;
    // wx.chooseMedia({
    //   count: 1,
    //   mediaType: ["video"],
    //   sourceType: ["album", "camera"],
    //   maxDuration: 30,
    //   success: (res) => {
    //     const tempFilePath = res.tempFiles[0].tempFilePath;
    //     this.setData({ videoUrl: tempFilePath });
    //     this.uploadVideo(tempFilePath);
    //   },
    // });
  },
  getAudioList() {
    const _this = this;
    PostData({
      url: "getAudioLst",
      params: {
        open_id: "admin",
        page: 1,
        pageSize: 10000,
      },
    }).then((res) => {
      console.log("audio:", res);
      if (res.code === 200) {
        const list = res.data?.list?.map((item) => {
          // {
          //   id: "a1",
          //   duration: "02:45",
          //   title: "专业女声解说",
          //   createTime: "2025-04-10 15:30:22",
          //   coverUrl: "/assets/images/audio-cover-1.png",
          //   audioUrl: "https://example.com/audio1.mp3",
          //   type: "female",
          //   description: "适合产品介绍、教程讲解等场景",
          // },
          return {
            id: item._id,
            uuid: item.uuid,
            duration: item.duration,
            title: item.title,
            createTime: item.create_at,
            coverUrl: item.cover,
            audioUrl: item.oss_url,
            description: item.description,
          };
        });
        console.log("audiosss", list);
        _this.setData({
          audioList: list,
        });
      }
    });
  },
  getVideoList() {
    const _this = this;
    PostData({
      url: "getVideoList",
      params: {
        open_id: "admin",
        page: 1,
        pageSize: 10,
      },
    }).then((res) => {
      if (res.code === 200) {
        const list = res.data?.list?.map((item) => {
          return {
            id: item._id,
            uuid: item.uuid,
            duration: item.duration,
            title: item.title,
            createTime: item.create_at,
            coverUrl: item.cover,
            videoUrl: item.oss_url,
          };
        });
        console.log("list::", list);
        _this.setData({
          videoList: list,
        });
      }
      console.log("ooooo", res);
    });
  },
  /** 删除视频 */
  deleteVideo(e) {
    const id = e.detail;
    console.log(e);
    if (!id) return;
    const _this = this;
    GetData({
      url: "deleteVideo",
      params: {
        id: id,
      },
    })
      .then((res) => {
        wx.showToast({
          title: "删除成功",
          icon: "success",
          duration: 1500,
          mask: true,
        });
        _this.getVideoList();
      })
      .catch((result) => {
        wx.showToast({
          title: `${result.code || ""}:${
            result?.message ?? ""
          },${result.details.join("、")}`,
          icon: "none",
          mask: true,
          duration: 2300,
        });
      });
  },
  /**
   * 添加音频模型
   */
  addAudioModel() {
    // wx.showToast({
    //   title: "添加音频模型功能即将上线",
    //   icon: "none",
    //   duration: 2000,
    // });
    // 实际应用中可以跳转到上传音频模型的页面
    wx.navigateTo({
      url: "/pages/AddMedia/index?type=audio",
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getVideoList();
    this.getAudioList();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
});
