// pages/PrivateModel/index.js

const {
  getUrl,
  PostData,
  GetData
} = require("../../common/server");
const {
  formatDuration
} = require("../../utils/utils");
Page({
  /**
   * 页面的初始数据
   */
  data: {
    activeTab: "video",
    selectedVideoId: "",
    selectedAudioId: "",
    // Audio playback related data
    currentPlayingIndex: -1,
    isPlaying: false,
    audioProgress: 0,
    currentTime: 0,
    totalTime: 0,
    currentTimeFormatted: "00:00",
    totalTimeFormatted: "00:00",
    audioContext: null,
    videoList: [
      // {
      //   id: "1",
      //   duration: "23:10",
      //   title: "坐着的安静-讲解法律知识妻女呢",
      //   createTime: "2025-04-09 12:03:01",
      //   coverUrl: "https://img.yzcdn.cn/vant/cat.jpeg",
      //   videoUrl:
      //     "https://wxsnsdy.tc.qq.com/105/20210/snsdyvideodownload?filekey=30280201010421301f0201690402534804102ca905ce620b1241b726bc41dcff44e00204012882540400&bizid=1023&hy=SH&fileparam=302c020101042530230204136ffd93020457e3c4ff02024ef202031e8d7f02030f42400204045a320a0201000400",
      // },
      // {
      //   id: "2",
      //   duration: "10:10",
      //   title: "坐着的安静-讲解法律知识成绩都是你",
      //   createTime: "2025-04-09 12:03:01",
      //   coverUrl: "https://img.yzcdn.cn/vant/cat.jpeg",
      //   videoUrl:
      //     "https://wxsnsdy.tc.qq.com/105/20210/snsdyvideodownload?filekey=30280201010421301f0201690402534804102ca905ce620b1241b726bc41dcff44e00204012882540400&bizid=1023&hy=SH&fileparam=302c020101042530230204136ffd93020457e3c4ff02024ef202031e8d7f02030f42400204045a320a0201000400",
      // },
      // {
      //   id: "3",
      //   duration: "9:10",
      //   title: "坐着的安静-讲解法律知识积极哦的风景",
      //   createTime: "2025-04-09 12:03:01",
      //   coverUrl: "https://img.yzcdn.cn/vant/cat.jpeg",
      //   videoUrl:
      //     "https://wxsnsdy.tc.qq.com/105/20210/snsdyvideodownload?filekey=30280201010421301f0201690402534804102ca905ce620b1241b726bc41dcff44e00204012882540400&bizid=1023&hy=SH&fileparam=302c020101042530230204136ffd93020457e3c4ff02024ef202031e8d7f02030f42400204045a320a0201000400",
      // },
    ],
    audioList: [
      // {
      //   id: "a1",
      //   duration: "02:45",
      //   title: "专业女声解说",
      //   createTime: "2025-04-10 15:30:22",
      //   coverUrl: "/assets/images/audio-cover-1.png",
      //   audioUrl: "https://example.com/audio1.mp3",
      //   type: "female",
      //   description: "适合产品介绍、教程讲解等场景",
      // },
      // {
      //   id: "a2",
      //   duration: "01:58",
      //   title: "男声商业配音",
      //   createTime: "2025-04-09 09:15:40",
      //   coverUrl: "/assets/images/audio-cover-2.png",
      //   audioUrl: "https://example.com/audio2.mp3",
      //   type: "male",
      //   description: "适合商业广告、企业宣传片等场景",
      // },
      // {
      //   id: "a3",
      //   duration: "03:12",
      //   title: "童声配音模型",
      //   createTime: "2025-04-08 14:22:10",
      //   coverUrl: "/assets/images/audio-cover-3.png",
      //   audioUrl: "https://example.com/audio3.mp3",
      //   type: "child",
      //   description: "适合儿童内容、卡通角色等场景",
      // },
      // {
      //   id: "a4",
      //   duration: "02:30",
      //   title: "情感女声模型",
      //   createTime: "2025-04-07 11:05:33",
      //   coverUrl: "/assets/images/audio-cover-4.png",
      //   audioUrl: "https://example.com/audio4.mp3",
      //   type: "female",
      //   description: "适合情感内容、故事叙述等场景",
      // },
    ],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 如果有tab参数，切换到对应的标签页
    if (options && options.tab) {
      this.setData({
        activeTab: options.tab,
      });
    }

    // 初始化音频上下文
    this.initAudioContext();
  },

  /**
   * 初始化音频上下文
   */
  initAudioContext() {
    const audioContext = wx.createInnerAudioContext();

    audioContext.onPlay(() => {
      console.log("音频开始播放");
      this.setData({
        isPlaying: true
      });
    });

    audioContext.onPause(() => {
      console.log("音频暂停");
      this.setData({
        isPlaying: false
      });
    });

    audioContext.onStop(() => {
      console.log("音频停止");
      this.setData({
        isPlaying: false,
        currentPlayingIndex: -1,
        audioProgress: 0,
        currentTime: 0,
      });
    });

    audioContext.onEnded(() => {
      console.log("音频播放结束");
      this.setData({
        isPlaying: false,
        currentPlayingIndex: -1,
        audioProgress: 0,
        currentTime: 0,
      });
    });

    audioContext.onTimeUpdate(() => {
      const currentTime = audioContext.currentTime;
      const duration = audioContext.duration;
      if (duration > 0) {
        const progress = (currentTime / duration) * 100;
        this.setData({
          audioProgress: progress,
          currentTime: currentTime,
          totalTime: duration,
          currentTimeFormatted: this.formatTime(currentTime),
          totalTimeFormatted: this.formatTime(duration),
        });
      }
    });

    audioContext.onError((res) => {
      console.error("音频播放错误:", res);
      wx.showToast({
        title: "音频播放失败",
        icon: "none",
      });
      this.setData({
        isPlaying: false,
        currentPlayingIndex: -1,
      });
    });

    this.setData({
      audioContext
    });
  },

  /**
   * 切换音频播放状态
   */
  toggleAudioPlay(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    const audioUrl = e.currentTarget.dataset.url;
    const {
      currentPlayingIndex,
      isPlaying,
      audioContext
    } = this.data;

    if (!audioUrl) {
      wx.showToast({
        title: "音频文件不存在",
        icon: "none",
      });
      return;
    }

    // 如果点击的是当前播放的音频
    if (currentPlayingIndex === index) {
      if (isPlaying) {
        audioContext.pause();
      } else {
        audioContext.play();
      }
    } else {
      // 停止当前播放的音频
      if (currentPlayingIndex !== -1) {
        audioContext.stop();
      }

      // 播放新的音频
      audioContext.src = audioUrl;
      audioContext.play();

      this.setData({
        currentPlayingIndex: index,
        audioProgress: 0,
        currentTime: 0,
        totalTime: 0,
      });
    }
  },

  /**
   * 音频进度条拖拽
   */
  seekAudio(e) {
    const {
      audioContext,
      totalTime
    } = this.data;
    if (!audioContext || totalTime <= 0) return;

    const {
      clientX
    } = e.detail;
    const query = wx.createSelectorQuery();
    query.select(".progress-bar").boundingClientRect();
    query.exec((res) => {
      if (res[0]) {
        const {
          left,
          width
        } = res[0];
        const progress = Math.max(0, Math.min(1, (clientX - left) / width));
        const seekTime = progress * totalTime;

        audioContext.seek(seekTime);
        this.setData({
          audioProgress: progress * 100,
          currentTime: seekTime,
          currentTimeFormatted: this.formatTime(seekTime),
        });
      }
    });
  },


  /**
   * 删除音频
   */
  deleteAudio(e) {
    const _this = this;
    const index = parseInt(e.currentTarget.dataset.index);
    const audio = this.data.audioList[index];

    wx.showModal({
      title: "确认删除",
      content: "删除后无法恢复，确定要删除这个音频模型吗？",
      success: (res) => {
        if (res.confirm) {
          GetData({
            url: "deleteAudio",
            params: {
              id: audio.uuid
            }
          }).then((res) => {
            wx.showToast({
              title: '删除成功！',
              icon: "success",
              mask: true,
            })
            // 重新获取音频列表
            _this.getAudioList();
          }).catch((res) => {
            wx.showToast({
              title: `${result.code || ""}:${
                result?.message ?? ""
              },${result.details.join("、")}`,
              icon: "none",
              mask: true,
              duration: 2300,
            });
          })

        }
      },
    });
  },

  /**
   * 重命名音频
   */
  // renameAudio(index) {
  //   const audio = this.data.audioList[index];
  //   wx.showModal({
  //     title: "重命名",
  //     content: "请输入新的名称",
  //     editable: true,
  //     placeholderText: audio.title,
  //     success: (res) => {
  //       if (res.confirm && res.content.trim()) {
  //         // 这里应该调用重命名API
  //         wx.showToast({
  //           title: "重命名成功",
  //           icon: "success",
  //         });
  //         // 重新获取音频列表
  //         this.getAudioList();
  //       }
  //     },
  //   });
  // },

  /**
   * 分享音频
   */
  // shareAudio(audio) {
  //   wx.showToast({
  //     title: "分享功能即将上线",
  //     icon: "none",
  //   });
  // },

  /**
   * 格式化时间显示
   */
  formatTime(seconds) {
    if (!seconds || isNaN(seconds)) return "00:00";

    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  },

  /**
   * 选择视频
   */
  selectVideo(e) {
    const index = e.currentTarget.dataset.index;
    const video = this.data.videoList[index];

    // 获取页面事件通道
    const eventChannel = this.getOpenerEventChannel();

    // 将选中的视频数据传回上一页
    if (eventChannel) {
      eventChannel.emit("selectVideoCallback", video);
    }
    wx.navigateBack();
  },

  /**
   * 选择音频
   */
  selectAudio(e) {
    const index = e.currentTarget.dataset.index;
    const audio = this.data.audioList[index];

    // 获取页面事件通道
    const eventChannel = this.getOpenerEventChannel();

    // 将选中的音频数据传回上一页
    if (eventChannel) {
      eventChannel.emit("selectAudioCallback", audio);
    }
    wx.navigateBack();
  },

  /**
   * 添加视频模型
   */
  addVideoModel() {
    // wx.showToast({
    //   title: "添加视频模型功能即将上线",
    //   icon: "none",
    //   duration: 2000,
    // });

    // 实际应用中可以跳转到上传视频模型的页面
    wx.navigateTo({
      url: "/pages/AddMedia/index?type=video",
    });
  },
  getAudioList() {
    const _this = this;
    PostData({
      url: "getAudioLst",
      params: {
        open_id: "admin",
        page: 1,
        pageSize: 10000,
      },
    }).then((res) => {
      console.log("audio:", res);
      if (res.code === 200) {
        const list = res.data?.list?.map((item) => {
          return {
            id: item._id,
            uuid: item.uuid,
            duration: formatDuration(item.duration),
            title: item.title,
            createTime: item.create_at,
            coverUrl: item.cover,
            audioUrl: item.oss_url,
            description: item.description,
          };
        });
        console.log("audiosss", list);
        _this.setData({
          audioList: list,
        });
      }
    });
  },
  getVideoList() {
    const _this = this;
    PostData({
      url: "getVideoList",
      params: {
        open_id: "admin",
        page: 1,
        pageSize: 10,
      },
    }).then((res) => {
      if (res.code === 200) {
        const list = res.data?.list?.map((item) => {
          return {
            id: item._id,
            uuid: item.uuid,
            duration: formatDuration(item.duration),
            title: item.title,
            createTime: item.create_at,
            coverUrl: item.cover,
            videoUrl: item.oss_url,
          };
        });
        console.log("list::", list);
        _this.setData({
          videoList: list,
        });
      }
      console.log("ooooo", res);
    });
  },
  /** 删除视频 */
  deleteVideo(e) {
    const id = e.detail;
    console.log(e);
    if (!id) return;
    const _this = this;
    GetData({
        url: "deleteVideo",
        params: {
          id: id,
        },
      })
      .then((res) => {
        wx.showToast({
          title: "删除成功",
          icon: "success",
          duration: 1500,
          mask: true,
        });
        _this.getVideoList();
      })
      .catch((result) => {
        wx.showToast({
          title: `${result.code || ""}:${
            result?.message ?? ""
          },${result.details.join("、")}`,
          icon: "none",
          mask: true,
          duration: 2300,
        });
      });
  },
  /**
   * 添加音频模型
   */
  addAudioModel() {
    // wx.showToast({
    //   title: "添加音频模型功能即将上线",
    //   icon: "none",
    //   duration: 2000,
    // });
    // 实际应用中可以跳转到上传音频模型的页面
    wx.navigateTo({
      url: "/pages/AddMedia/index?type=audio",
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getVideoList();
    this.getAudioList();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 清理音频上下文
    if (this.data.audioContext) {
      this.data.audioContext.destroy();
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
});