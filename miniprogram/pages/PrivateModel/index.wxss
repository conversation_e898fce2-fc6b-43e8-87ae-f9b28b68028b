page {
  height: 100%;
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

/* Main content area */
.main {
  padding: 24rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
  height: calc(100% - 74rpx);
  overflow-y: auto;
}

/* Add model button styling */
.add-model-container {
  width: 100%;
  margin-bottom: 16rpx;
}

.add-model-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border: 2rpx dashed #ddd;
  border-radius: 16rpx;
  padding: 24rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.add-model-btn:active {
  background-color: #f9f9f9;
  border-color: #ff7f11;
}

.add-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background-color: rgba(255, 127, 17, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.add-text {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

/* Original video item styling */
.video_item {
  width: calc(50% - 16rpx);
  flex-shrink: 1;
  flex-grow: 0;
  background-color: #fff;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  overflow: hidden;
}

.video_content {
  position: relative;
  width: 100%;
  aspect-ratio: 9/10;
  background-color: #f0f0f0;
}

.video_content image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video_foot {
  padding: 12rpx;
}

.video_title {
  font-size: 28rpx;
  height: 82rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
}

.video_time {
  font-size: 28rpx;
  color: #999;
  margin-top: 12rpx;
}

.play-icon {
  position: absolute;
  width: 52rpx;
  height: 52rpx;
  z-index: 100;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
}

.video-duration {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

/* Audio List Styling */
.audio-list {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.audio-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1rpx solid transparent;
}

.audio-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  border-color: rgba(255, 127, 17, 0.2);
}

/* Audio Info Section */
.audio-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.audio-avatar {
  position: relative;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.avatar-bg {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.avatar-bg.female {
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
}

.avatar-bg.male {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.avatar-bg.child {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.avatar-bg.default {
  background: linear-gradient(135deg, #ff7f11, #ff5500);
}

.audio-icon {
  z-index: 2;
}

/* Play Indicator */
.play-indicator {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.play-indicator.playing {
  opacity: 1;
}

.wave-animation {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4rpx;
}

.wave-bar {
  width: 4rpx;
  background-color: #fff;
  border-radius: 2rpx;
  animation: wave-pulse 1.2s infinite ease-in-out;
}

.wave1 {
  animation-delay: 0s;
  height: 16rpx;
}

.wave2 {
  animation-delay: 0.1s;
  height: 24rpx;
}

.wave3 {
  animation-delay: 0.2s;
  height: 20rpx;
}

.wave4 {
  animation-delay: 0.3s;
  height: 18rpx;
}

@keyframes wave-pulse {

  0%,
  100% {
    transform: scaleY(0.5);
    opacity: 0.7;
  }

  50% {
    transform: scaleY(1);
    opacity: 1;
  }
}

/* Audio Details */
.audio-details {
  flex: 1;
  min-width: 0;
}

.audio-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.audio-meta {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
  flex-wrap: wrap;
}

.audio-separator {
  margin: 0 8rpx;
}

.audio-duration {
  color: #ff7f11;
  font-weight: 500;
}

.audio-type {
  color: #666;
}

.audio-time {
  color: #999;
}

.audio-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Audio Controls */
.audio-controls {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f5f5f5;
}

.control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.play-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background-color: rgba(255, 127, 17, 0.1);
  flex-shrink: 0;
}

.play-btn:active {
  transform: scale(0.95);
  background-color: rgba(255, 127, 17, 0.2);
}

.more-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background-color: rgba(255, 127, 17, 0.1);
  flex-shrink: 0;
}

.more-btn:active {
  background-color: #e8e8e8;
}

/* Progress Container */
.progress-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.progress-bar {
  position: relative;
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff7f11, #ff5500);
  border-radius: 4rpx;
  transition: width 0.1s ease;
}

.progress-thumb {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 16rpx;
  height: 16rpx;
  background-color: #ff7f11;
  border-radius: 50%;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  transition: left 0.1s ease;
}

.time-display {
  display: flex;
  justify-content: space-between;
  font-size: 22rpx;
  color: #999;
}

.current-time {
  color: #ff7f11;
}

/* Empty state styling */
.empty-state {
  width: 100%;
  grid-column: span 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  margin: 40rpx 0;
  position: relative;
  overflow: hidden;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.8;
}

.empty-text {
  font-size: 32rpx;
  color: #333333;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 40rpx;
  text-align: center;
  max-width: 80%;
  line-height: 1.5;
}

.empty-action {
  margin-top: 30rpx;
}

.add-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  color: #ffffff;
  font-size: 28rpx;
  padding: 0 40rpx;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  border: none;
  box-shadow: 0 6rpx 12rpx rgba(255, 127, 17, 0.2);
  font-weight: 500;
  transition: all 0.3s ease;
}

.add-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(255, 127, 17, 0.3);
}

.add-button text {
  margin-left: 10rpx;
}