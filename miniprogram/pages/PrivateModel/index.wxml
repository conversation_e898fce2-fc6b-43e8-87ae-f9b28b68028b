<van-tabs active="{{ activeTab }}" title-active-color="#FF7F11" sticky="true" z-index="100" color="#FF7F11" animated swipeable>
  <van-tab title="视频模型" name="video">
    <view class="main">
      <!-- 添加视频模型按钮 -->
      <view class="add-model-container">
        <view class="add-model-btn" bindtap="addVideoModel">
          <view class="add-icon">
            <van-icon name="plus" size="48rpx" color="#FF7F11" />
          </view>
          <view class="add-text">添加视频模型</view>
        </view>
      </view>

      <!-- 视频模型列表 -->
      <block wx:if="{{videoList.length > 0}}">
        <block wx:for="{{videoList}}" wx:key="unique">
          <sc-video-ele item="{{item}}" bind:deleteVideo="deleteVideo" delete="{{true}}" />
        </block>
      </block>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{videoList.length === 0}}">
        <image class="empty-icon" src="" mode="aspectFit"></image>
        <view class="empty-text">还没有上传视频模型</view>
        <view class="empty-desc">添加视频模型后可用于创作视频内容</view>
      </view>
    </view>
  </van-tab>

  <van-tab title="音频模型" name="audio">
    <view class="main">
      <!-- 添加音频模型按钮 -->
      <view class="add-model-container">
        <view class="add-model-btn" bindtap="addAudioModel">
          <view class="add-icon">
            <van-icon name="plus" size="48rpx" color="#FF7F11" />
          </view>
          <view class="add-text">添加音频模型</view>
        </view>
      </view>

      <!-- 音频模型列表 -->
      <block wx:if="{{audioList.length > 0}}">
        <view class="model-item audio-item" wx:for="{{audioList}}" wx:key="id" bindtap="selectAudio" data-index="{{index}}">
          <view class="model-cover audio-cover">
            <image src="{{item.coverUrl || ''}}" mode="aspectFill"></image>
            <view class="model-duration">{{item.duration}}</view>
            <view class="model-type-tag {{item.type}}">{{item.type === 'female' ? '女声' : item.type === 'male' ? '男声' : '童声'}}</view>
            <view class="model-play-icon">
              <van-icon name="service-o" size="48rpx" color="#fff" />
              <view class="audio-waves">
                <view class="wave wave1"></view>
                <view class="wave wave2"></view>
                <view class="wave wave3"></view>
                <view class="wave wave4"></view>
              </view>
            </view>
          </view>
          <view class="model-info">
            <view class="model-title">{{item.title}}</view>
            <view class="model-desc">{{item.description}}</view>
            <view class="audio-controls">
              <view class="audio-play-btn">
                <van-icon name="play-circle-o" size="40rpx" color="#FF7F11" />
              </view>
              <view class="model-time">{{item.createTime}}</view>
            </view>
          </view>
        </view>
      </block>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{audioList.length === 0}}">
        <image class="empty-icon" src="" mode="aspectFit"></image>
        <view class="empty-text">还没有上传音频模型</view>
        <view class="empty-desc">添加音频模型后可用于创作音频内容</view>
      </view>
    </view>
  </van-tab>
</van-tabs>