<van-tabs active="{{ activeTab }}" title-active-color="#FF7F11" sticky="true" z-index="100" color="#FF7F11" animated swipeable>
  <van-tab title="视频模型" name="video">
    <view class="main">
      <!-- 添加视频模型按钮 -->
      <view class="add-model-container">
        <view class="add-model-btn" bindtap="addVideoModel">
          <view class="add-icon">
            <van-icon name="plus" size="48rpx" color="#FF7F11" />
          </view>
          <view class="add-text">添加视频模型</view>
        </view>
      </view>

      <!-- 视频模型列表 -->
      <block wx:if="{{videoList.length > 0}}">
        <block wx:for="{{videoList}}" wx:key="unique">
          <sc-video-ele item="{{item}}" bind:deleteVideo="deleteVideo" delete="{{true}}" />
        </block>
      </block>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{videoList.length === 0}}">
        <image class="empty-icon" src="" mode="aspectFit"></image>
        <view class="empty-text">还没有上传视频模型</view>
        <view class="empty-desc">添加视频模型后可用于创作视频内容</view>
      </view>
    </view>
  </van-tab>

  <van-tab title="音频模型" name="audio">
    <view class="main">
      <!-- 添加音频模型按钮 -->
      <view class="add-model-container">
        <view class="add-model-btn" bindtap="addAudioModel">
          <view class="add-icon">
            <van-icon name="plus" size="48rpx" color="#FF7F11" />
          </view>
          <view class="add-text">添加音频模型</view>
        </view>
      </view>
      <!-- 音频模型列表 -->
      <view class="audio-list" wx:if="{{audioList.length > 0}}">
        <view class="audio-item" wx:for="{{audioList}}" wx:key="id" data-index="{{index}}">
          <!-- 音频信息区域 -->
          <view class="audio-info" bindtap="selectAudio" data-index="{{index}}">
            <view class="audio-avatar">
              <view class="avatar-bg {{item.type || 'default'}}">
                <view class="audio-icon">
                  <van-icon name="music-o" size="32rpx" color="#fff" />
                </view>
              </view>
              <!-- 播放状态指示器 -->
              <view class="play-indicator {{currentPlayingIndex === index ? 'playing' : ''}}" wx:if="{{currentPlayingIndex === index}}">
                <view class="wave-animation">
                  <view class="wave-bar wave1"></view>
                  <view class="wave-bar wave2"></view>
                  <view class="wave-bar wave3"></view>
                  <view class="wave-bar wave4"></view>
                </view>
              </view>
            </view>

            <view class="audio-details">
              <view class="audio-title">{{item.title}}</view>
              <view class="audio-meta">
                <text class="audio-duration">{{item.duration || '00:00'}}</text>
                <text class="audio-separator">•</text>
                <text class="audio-type">{{item.type === 'female' ? '女声' : item.type === 'male' ? '男声' : item.type === 'child' ? '童声' : '通用'}}</text>
                <text class="audio-separator">•</text>
                <text class="audio-time">{{item.createTime}}</text>
              </view>
              <view class="audio-description" wx:if="{{item.description}}">{{item.description}}</view>
            </view>
          </view>

          <!-- 播放控制区域 -->
          <view class="audio-controls">
            <view class="control-btn play-btn" bindtap="toggleAudioPlay" data-index="{{index}}" data-url="{{item.audioUrl}}">
              <van-icon name="{{currentPlayingIndex === index && isPlaying ? 'pause-circle-o' : 'play-circle-o'}}" size="48rpx" color="#FF7F11" />
            </view>

            <!-- 进度条 (仅在播放时显示) -->
            <view class="progress-container" style="visibility: {{currentPlayingIndex === index?'visible':'hidden'}};">
              <view class="progress-bar" bindtap="seekAudio" data-index="{{index}}">
                <view class="progress-fill" style="width: {{audioProgress}}%"></view>
                <view class="progress-thumb" style="left: {{audioProgress}}%"></view>
              </view>
              <view class="time-display">
                <text class="current-time">{{currentTimeFormatted}}</text>
                <text class="total-time">{{totalTimeFormatted}}</text>
              </view>
            </view>

            <!-- 更多操作按钮 -->
            <view class="control-btn more-btn" bindtap="deleteAudio" data-index="{{index}}">
              <van-icon name="delete" size="30rpx" color="red" />
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{audioList.length === 0}}">
        <image class="empty-icon" src="" mode="aspectFit"></image>
        <view class="empty-text">还没有上传音频模型</view>
        <view class="empty-desc">添加音频模型后可用于创作音频内容</view>
      </view>
    </view>
  </van-tab>
</van-tabs>