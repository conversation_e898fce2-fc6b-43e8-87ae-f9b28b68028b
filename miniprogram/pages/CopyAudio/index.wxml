<view class="container">
  <!-- Header -->
  <view class="header">
    <view class="header-title">情感录音</view>
    <view class="header-subtitle">朗读不同情感的文案，训练你的专属声音模型</view>
  </view>

  <!-- Main Content -->
  <scroll-view scroll-y class="content" enhanced show-scrollbar="{{false}}">

    <!-- Progress Indicator -->
    <view class="progress-section">
      <view class="progress-header">
        <view class="progress-title">录制进度</view>
        <view class="progress-count">{{completedCount}}/{{emotionalTexts.length}}</view>
      </view>
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{(completedCount / emotionalTexts.length) * 100}}%"></view>
      </view>
      <view class="progress-desc">完成所有情感录音，获得更好的声音克隆效果</view>
    </view>

    <!-- Emotional Text Cards -->
    <view class="text-cards">
      <view class="text-card {{item.status === 'completed' ? 'completed' : item.status === 'recording' ? 'recording' : ''}}"
            wx:for="{{emotionalTexts}}"
            wx:key="id"
            bindtap="selectText"
            data-index="{{index}}">

        <!-- Card Header -->
        <view class="card-header">
          <view class="emotion-info">
            <view class="emotion-icon {{item.emotion}}">
              <van-icon name="{{item.icon}}" size="24rpx" color="#fff" />
            </view>
            <view class="emotion-details">
              <view class="emotion-name">{{item.emotionName}}</view>
              <view class="emotion-desc">{{item.description}}</view>
            </view>
          </view>

          <view class="card-status">
            <view class="status-icon {{item.status}}" wx:if="{{item.status === 'completed'}}">
              <van-icon name="success" size="20rpx" color="#fff" />
            </view>
            <view class="status-icon recording" wx:elif="{{item.status === 'recording'}}">
              <van-icon name="volume-o" size="20rpx" color="#fff" />
            </view>
            <view class="status-text" wx:else>待录制</view>
          </view>
        </view>

        <!-- Text Content -->
        <view class="text-content">
          <view class="text-label">朗读文案：</view>
          <view class="text-body">{{item.text}}</view>
        </view>

        <!-- Recording Controls (only show for selected card) -->
        <view class="recording-controls" wx:if="{{selectedIndex === index}}">

          <!-- Recording Area -->
          <view class="recording-area">
            <view class="recording-visual {{isRecording ? 'active' : ''}}">
              <view class="wave-container">
                <view class="wave-circle wave1"></view>
                <view class="wave-circle wave2"></view>
                <view class="wave-circle wave3"></view>
              </view>
              <view class="record-button" bindtap="toggleRecording">
                <van-icon name="{{isRecording ? 'pause-circle-o' : 'play-circle-o'}}" size="32rpx" color="#fff" />
              </view>
            </view>

            <view class="recording-info">
              <view class="recording-time">{{recordingTime}}</view>
              <view class="recording-status">
                {{isRecording ? '正在录制...' : (hasRecording ? '录制完成' : '点击开始录制')}}
              </view>
            </view>
          </view>

          <!-- Playback Controls (show when has recording) -->
          <view class="playback-controls" wx:if="{{hasRecording}}">
            <view class="playback-info">
              <view class="playback-duration">时长: {{recordingDuration}}</view>
              <view class="playback-quality">质量: {{recordingQuality}}</view>
            </view>

            <view class="playback-buttons">
              <button class="control-btn secondary" bindtap="playRecording">
                <van-icon name="{{isPlaying ? 'pause-circle-o' : 'play-circle-o'}}" size="16rpx" />
                <text>{{isPlaying ? '暂停' : '试听'}}</text>
              </button>

              <button class="control-btn secondary" bindtap="reRecord">
                <van-icon name="replay" size="16rpx" />
                <text>重录</text>
              </button>

              <button class="control-btn primary" bindtap="confirmRecording">
                <van-icon name="success" size="16rpx" />
                <text>确认</text>
              </button>
            </view>
          </view>

          <!-- Action Buttons -->
          <view class="action-buttons" wx:if="{{!hasRecording}}">
            <button class="control-btn secondary" bindtap="cancelSelection">
              <van-icon name="close" size="16rpx" />
              <text>取消</text>
            </button>

            <button class="control-btn primary" bindtap="startRecording" disabled="{{isRecording}}">
              <van-icon name="volume-o" size="16rpx" />
              <text>开始录制</text>
            </button>
          </view>
        </view>

        <!-- Completed Recording Info -->
        <view class="completed-info" wx:if="{{item.status === 'completed' && selectedIndex !== index}}">
          <view class="completed-duration">录制时长: {{item.duration}}</view>
          <view class="completed-actions">
            <button class="mini-btn" bindtap="playCompleted" data-index="{{index}}">
              <van-icon name="play-circle-o" size="14rpx" />
              <text>播放</text>
            </button>
            <button class="mini-btn" bindtap="reRecordCompleted" data-index="{{index}}">
              <van-icon name="replay" size="14rpx" />
              <text>重录</text>
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- Tips Section -->
    <view class="tips-section">
      <view class="tips-header">
        <van-icon name="info-o" size="20rpx" color="#FF7F11" />
        <text>录制小贴士</text>
      </view>
      <view class="tips-list">
        <view class="tip-item">• 请在安静的环境中录制，避免背景噪音</view>
        <view class="tip-item">• 保持正常语速，清晰发音</view>
        <view class="tip-item">• 根据情感类型调整语调和情绪</view>
        <view class="tip-item">• 每段录音建议3-10秒，过短或过长都会影响效果</view>
        <view class="tip-item">• 完成所有情感录音可获得最佳声音克隆效果</view>
      </view>
    </view>

  </scroll-view>

  <!-- Bottom Actions -->
  <view class="bottom-actions">
    <button class="action-button secondary" bindtap="saveProgress">
      <van-icon name="bookmark-o" size="16rpx" />
      <text>保存进度</text>
    </button>

    <button
      class="action-button primary"
      bindtap="completeRecording"
      disabled="{{completedCount < emotionalTexts.length}}"
    >
      <van-icon name="success" size="16rpx" />
      <text>完成录制 ({{completedCount}}/{{emotionalTexts.length}})</text>
    </button>
  </view>
</view>