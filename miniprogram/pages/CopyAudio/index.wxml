<view class="container">
  <!-- Header -->
  <view class="header">
    <view class="header-title">音频录制</view>
    <view class="header-subtitle">选择文案进行录制，支持回放试听</view>
  </view>

  <!-- Tab Navigation -->
  <view class="tab-container">
    <scroll-view scroll-x class="tab-scroll" show-scrollbar="{{false}}">
      <view class="tab-list">
        <view
          class="tab-item {{activeTab === index ? 'active' : ''}}"
          wx:for="{{textCategories}}"
          wx:key="id"
          bindtap="switchTab"
          data-index="{{index}}"
        >
          <view class="tab-icon">
            <van-icon name="{{item.icon}}" size="20rpx" color="{{activeTab === index ? '#fff' : '#666'}}" />
          </view>
          <view class="tab-text">{{item.name}}</view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- Content Area -->
  <view class="content">
    <!-- Text Display -->
    <view class="text-section">
      <view class="text-header">
        <view class="text-title">{{currentText.title}}</view>
        <view class="text-category">{{textCategories[activeTab].name}}</view>
      </view>
      <view class="text-content">
        <text class="text-body">{{currentText.content}}</text>
      </view>
    </view>

    <!-- Recording Area -->
    <view class="recording-section">
      <view class="recording-visual {{isRecording ? 'recording' : ''}}">
        <view class="wave-container">
          <view class="wave-circle wave1"></view>
          <view class="wave-circle wave2"></view>
          <view class="wave-circle wave3"></view>
        </view>
        <view class="record-button" bindtap="toggleRecording">
          <van-icon name="{{isRecording ? 'pause-circle-o' : 'volume-o'}}" size="40rpx" color="#fff" />
        </view>
      </view>

      <view class="recording-info">
        <view class="recording-time">{{recordingTime}}</view>
        <view class="recording-status">
          {{isRecording ? '正在录制中...' : (hasRecording ? '录制完成' : '点击开始录制')}}
        </view>
      </view>
    </view>

    <!-- Control Buttons -->
    <view class="control-section">
      <button class="control-btn secondary" bindtap="clearRecording" disabled="{{!hasRecording || isRecording}}">
        <van-icon name="delete-o" size="18rpx" />
        <text>清除</text>
      </button>

      <button class="control-btn primary" bindtap="playRecording" disabled="{{!hasRecording || isRecording}}">
        <van-icon name="{{isPlaying ? 'pause-circle-o' : 'play-circle-o'}}" size="18rpx" />
        <text>{{isPlaying ? '暂停' : '试听'}}</text>
      </button>

      <button class="control-btn success" bindtap="saveRecording" disabled="{{!hasRecording || isRecording}}">
        <van-icon name="success" size="18rpx" />
        <text>保存</text>
      </button>
    </view>

    <!-- Tips -->
    <view class="tips-section">
      <view class="tips-header">
        <van-icon name="info-o" size="16rpx" color="#FF7F11" />
        <text>录制提示</text>
      </view>
      <view class="tips-content">
        <text>• 请在安静环境中录制，保持正常语速</text>
        <text>• 根据文案内容调整语调和情感</text>
        <text>• 录制完成后可试听效果</text>
      </view>
    </view>
  </view>
</view>