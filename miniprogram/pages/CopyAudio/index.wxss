/* pages/CopyAudio/index.wxss */

/* Global styles */
page {
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  height: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  box-sizing: border-box;
}

/* Header styles */
.header {
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  padding: 40rpx 30rpx;
  color: #fff;
  box-shadow: 0 2rpx 12rpx rgba(255, 127, 17, 0.2);
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 26rpx;
  opacity: 0.9;
  line-height: 1.4;
}

/* Content area */
.content {
  flex: 1;
  padding: 30rpx;
  padding-bottom: 140rpx; /* Space for bottom actions */
}

/* Progress section */
.progress-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.progress-count {
  font-size: 28rpx;
  color: #ff7f11;
  font-weight: 600;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background-color: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 12rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff7f11, #ff5500);
  border-radius: 6rpx;
  transition: width 0.5s ease;
}

.progress-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* Text cards */
.text-cards {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.text-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  position: relative;
}

.text-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.text-card.completed {
  border-color: #28a745;
  background: linear-gradient(135deg, #fff, #f8fff9);
}

.text-card.recording {
  border-color: #ff7f11;
  background: linear-gradient(135deg, #fff, #fff8f4);
}

/* Card header */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.emotion-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.emotion-icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.emotion-icon.happy {
  background: linear-gradient(135deg, #ffd93d, #ff8c00);
}

.emotion-icon.sad {
  background: linear-gradient(135deg, #6c7ce0, #4a90e2);
}

.emotion-icon.angry {
  background: linear-gradient(135deg, #ff6b6b, #e74c3c);
}

.emotion-icon.excited {
  background: linear-gradient(135deg, #ff7f11, #ff5500);
}

.emotion-icon.calm {
  background: linear-gradient(135deg, #5ad8a6, #00d4aa);
}

.emotion-icon.surprised {
  background: linear-gradient(135deg, #9c88ff, #7b68ee);
}

.emotion-icon.gentle {
  background: linear-gradient(135deg, #ffb6c1, #ff69b4);
}

.emotion-icon.serious {
  background: linear-gradient(135deg, #708090, #2f4f4f);
}

.emotion-details {
  flex: 1;
}

.emotion-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.emotion-desc {
  font-size: 24rpx;
  color: #666;
}

.card-status {
  display: flex;
  align-items: center;
}

.status-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-icon.completed {
  background-color: #28a745;
}

.status-icon.recording {
  background-color: #ff7f11;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.status-text {
  font-size: 24rpx;
  color: #999;
  padding: 8rpx 16rpx;
  background-color: #f0f0f0;
  border-radius: 16rpx;
}

/* Text content */
.text-content {
  margin-bottom: 20rpx;
}

.text-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.text-body {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #ff7f11;
}

/* Recording controls */
.recording-controls {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.recording-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 16rpx;
  margin-bottom: 24rpx;
}

.recording-visual {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.wave-container {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wave-circle {
  position: absolute;
  border: 2rpx solid rgba(255, 127, 17, 0.3);
  border-radius: 50%;
  opacity: 0;
}

.recording-visual.active .wave-circle {
  animation: wave-pulse 2s infinite;
}

.wave1 {
  width: 100rpx;
  height: 100rpx;
  animation-delay: 0s;
}

.wave2 {
  width: 130rpx;
  height: 130rpx;
  animation-delay: 0.5s;
}

.wave3 {
  width: 160rpx;
  height: 160rpx;
  animation-delay: 1s;
}

@keyframes wave-pulse {
  0% {
    transform: scale(0.5);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

.record-button {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(255, 127, 17, 0.3);
  z-index: 2;
  transition: all 0.3s ease;
}

.record-button:active {
  transform: scale(0.95);
}

.recording-info {
  text-align: center;
}

.recording-time {
  font-size: 40rpx;
  font-weight: 600;
  color: #ff7f11;
  margin-bottom: 8rpx;
}

.recording-status {
  font-size: 24rpx;
  color: #666;
}

/* Playback controls */
.playback-controls {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
}

.playback-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
  font-size: 24rpx;
  color: #666;
}

.playback-duration {
  color: #ff7f11;
  font-weight: 500;
}

.playback-quality {
  color: #28a745;
}

.playback-buttons {
  display: flex;
  gap: 12rpx;
}

/* Control buttons */
.control-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  border: none;
  transition: all 0.3s ease;
  flex: 1;
  justify-content: center;
}

.control-btn.secondary {
  background-color: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
}

.control-btn.primary {
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  color: #fff;
}

.control-btn[disabled] {
  opacity: 0.5;
  pointer-events: none;
}

.control-btn:active {
  transform: scale(0.98);
}

.action-buttons {
  display: flex;
  gap: 16rpx;
}

/* Completed info */
.completed-info {
  margin-top: 16rpx;
  padding: 16rpx;
  background-color: #f8fff9;
  border-radius: 12rpx;
  border: 1rpx solid #d4edda;
}

.completed-duration {
  font-size: 24rpx;
  color: #28a745;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.completed-actions {
  display: flex;
  gap: 12rpx;
}

.mini-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  background-color: #fff;
  color: #666;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.mini-btn:active {
  background-color: #f8f9fa;
}

/* Tips section */
.tips-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.tips-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.tip-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  padding-left: 8rpx;
}

/* Bottom actions */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 16rpx;
  padding: 20rpx 30rpx calc(20rpx + env(safe-area-inset-bottom));
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.action-button.secondary {
  flex: 0 0 auto;
  padding: 0 32rpx;
  background-color: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
}

.action-button.primary {
  flex: 1;
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(255, 127, 17, 0.2);
}

.action-button[disabled] {
  background-color: #f0f0f0;
  color: #ccc;
  box-shadow: none;
  pointer-events: none;
}

.action-button:active {
  transform: scale(0.98);
}

/* Responsive adjustments */
@media (max-width: 750rpx) {
  .playback-buttons {
    flex-direction: column;
  }

  .action-buttons {
    flex-direction: column;
  }

  .completed-actions {
    flex-direction: column;
  }
}
