/* pages/CopyAudio/index.wxss */

/* Global styles */
page {
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  height: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}

/* Header styles */
.header {
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  padding: 40rpx 30rpx;
  color: #fff;
  box-shadow: 0 2rpx 12rpx rgba(255, 127, 17, 0.2);
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 26rpx;
  opacity: 0.9;
  line-height: 1.4;
}

/* Tab container */
.tab-container {
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.tab-scroll {
  white-space: nowrap;
}

.tab-list {
  display: flex;
  padding: 0 20rpx;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 24rpx;
  margin: 0 8rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  min-width: 120rpx;
  position: relative;
}

.tab-item.active {
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  color: #fff;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 127, 17, 0.3);
}

.tab-icon {
  margin-bottom: 8rpx;
}

.tab-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.tab-item.active .tab-text {
  color: #fff;
}

/* Content area */
.content {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
}

/* Text section */
.text-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.text-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.text-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.text-category {
  font-size: 24rpx;
  color: #ff7f11;
  background-color: rgba(255, 127, 17, 0.1);
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
}

.text-content {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  border-left: 4rpx solid #ff7f11;
}

.text-body {
  font-size: 30rpx;
  color: #333;
  line-height: 1.8;
  text-align: justify;
}

/* Recording section */
.recording-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  text-align: center;
}

.recording-visual {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  margin: 0 auto 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wave-container {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wave-circle {
  position: absolute;
  border: 2rpx solid rgba(255, 127, 17, 0.3);
  border-radius: 50%;
  opacity: 0;
}

.recording-visual.recording .wave-circle {
  animation: wave-pulse 2s infinite;
}

.wave1 {
  width: 120rpx;
  height: 120rpx;
  animation-delay: 0s;
}

.wave2 {
  width: 160rpx;
  height: 160rpx;
  animation-delay: 0.5s;
}

.wave3 {
  width: 200rpx;
  height: 200rpx;
  animation-delay: 1s;
}

@keyframes wave-pulse {
  0% {
    transform: scale(0.5);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

.record-button {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(255, 127, 17, 0.3);
  z-index: 2;
  transition: all 0.3s ease;
}

.record-button:active {
  transform: scale(0.95);
}

.recording-info {
  text-align: center;
}

.recording-time {
  font-size: 48rpx;
  font-weight: 600;
  color: #ff7f11;
  margin-bottom: 8rpx;
}

.recording-status {
  font-size: 26rpx;
  color: #666;
}

/* Control section */
.control-section {
  display: flex;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.control-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  border-radius: 32rpx;
  font-size: 26rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.control-btn.secondary {
  background-color: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
}

.control-btn.primary {
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  color: #fff;
}

.control-btn.success {
  background-color: #28a745;
  color: #fff;
}

.control-btn[disabled] {
  opacity: 0.5;
  pointer-events: none;
}

.control-btn:active {
  transform: scale(0.98);
}

/* Tips section */
.tips-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.tips-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.tips-content text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* Save Dialog Styles */
.save-dialog {
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.dialog-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.dialog-close {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f5f5f5;
  transition: all 0.3s ease;
}

.dialog-close:active {
  background-color: #e8e8e8;
  transform: scale(0.9);
}

.dialog-content {
  padding: 32rpx;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-item.error .form-input input,
.form-item.error .form-textarea textarea {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2rpx rgba(255, 77, 79, 0.1);
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.required {
  color: #ff4d4f;
  margin-right: 6rpx;
  font-size: 28rpx;
}

.form-input input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f9f9f9;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.form-input input:focus {
  border-color: #ff7f11;
  background-color: #fff;
  box-shadow: 0 0 0 2rpx rgba(255, 127, 17, 0.1);
}

.form-textarea textarea {
  width: 100%;
  min-height: 120rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f9f9f9;
  box-sizing: border-box;
  line-height: 1.5;
  transition: all 0.3s ease;
}

.form-textarea textarea:focus {
  border-color: #ff7f11;
  background-color: #fff;
  box-shadow: 0 0 0 2rpx rgba(255, 127, 17, 0.1);
}

.error-message {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #ff4d4f;
  line-height: 1.4;
}

.dialog-actions {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.dialog-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-btn.cancel {
  background-color: #f5f5f5;
  color: #666;
}

.dialog-btn.cancel:active {
  background-color: #e8e8e8;
  transform: scale(0.98);
}

.dialog-btn.confirm {
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 127, 17, 0.2);
}

.dialog-btn.confirm:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(255, 127, 17, 0.3);
}
