// pages/CopyAudio/index.js
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 当前选中的tab
    activeTab: 0,

    // 录音相关状态
    isRecording: false,
    hasRecording: false,
    isPlaying: false,
    recordingTime: "00:00",
    recordingTimer: null,
    recordingStartTime: 0,

    // 录音管理器和音频上下文
    recorderManager: null,
    audioContext: null,
    currentRecordingPath: "",

    // 文案分类数据
    textCategories: [
      {
        id: 1,
        name: "商务正式",
        icon: "manager-o",
        texts: [
          {
            id: 1,
            title: "企业介绍",
            content:
              "我们公司成立于2010年，是一家专注于人工智能技术研发的高新技术企业。经过十多年的发展，我们已经成为行业内的领军企业，拥有完整的产品线和优秀的技术团队。我们致力于为客户提供最优质的服务和最先进的技术解决方案。",
          },
          {
            id: 2,
            title: "产品发布",
            content:
              "今天，我们非常荣幸地向大家介绍我们的最新产品。这款产品融合了最前沿的人工智能技术，具有强大的功能和出色的性能。它不仅能够满足用户的基本需求，更能为用户带来全新的体验。我们相信这款产品将会改变整个行业的发展格局。",
          },
        ],
      },
      {
        id: 2,
        name: "生活日常",
        icon: "home-o",
        texts: [
          {
            id: 3,
            title: "美食分享",
            content:
              "今天给大家分享一道简单又美味的家常菜。首先准备新鲜的食材，然后按照步骤进行制作。这道菜不仅营养丰富，而且制作简单，非常适合忙碌的上班族。相信大家学会之后，一定会爱上这道菜的美味。让我们一起享受烹饪的乐趣吧！",
          },
          {
            id: 4,
            title: "旅行见闻",
            content:
              "这次旅行真是收获满满！我们去了很多美丽的地方，看到了壮观的自然风光，体验了当地的风土人情。每一个景点都有它独特的魅力，每一次经历都让人难忘。旅行不仅开阔了视野，更让我们感受到了世界的美好。期待下一次的旅行冒险！",
          },
        ],
      },
      {
        id: 3,
        name: "教育培训",
        icon: "certificate",
        texts: [
          {
            id: 5,
            title: "学习方法",
            content:
              "有效的学习方法对于提高学习效率非常重要。首先要制定合理的学习计划，然后要保持专注和耐心。在学习过程中，要善于总结和反思，及时发现问题并加以改正。同时，要注重理论与实践的结合，通过实际操作来加深理解。只有这样，才能真正掌握知识。",
          },
          {
            id: 6,
            title: "技能提升",
            content:
              "在这个快速发展的时代，持续学习和技能提升变得越来越重要。我们需要不断更新自己的知识结构，学习新的技能和方法。通过参加培训课程、阅读专业书籍、实践操作等方式，我们可以不断提升自己的能力。只有保持学习的热情，才能在竞争中立于不败之地。",
          },
        ],
      },
      {
        id: 4,
        name: "情感表达",
        icon: "like-o",
        texts: [
          {
            id: 7,
            title: "感谢话语",
            content:
              "在这个特殊的时刻，我想对所有帮助过我的人表示最真诚的感谢。是你们的支持和鼓励，让我能够克服困难，取得今天的成就。每一份帮助都深深地印在我的心里，每一句鼓励都给了我前进的动力。感谢有你们，让我的人生变得更加精彩。",
          },
          {
            id: 8,
            title: "祝福寄语",
            content:
              "愿你的每一天都充满阳光和快乐，愿你的梦想都能够实现。无论遇到什么困难，都要保持乐观的心态，相信明天会更好。愿友谊之花永远绽放，愿幸福之树常青。在人生的道路上，愿你勇敢前行，收获满满的喜悦和成功。祝你一切顺利，身体健康！",
          },
        ],
      },
      {
        id: 5,
        name: "新闻播报",
        icon: "volume-o",
        texts: [
          {
            id: 9,
            title: "科技资讯",
            content:
              "据最新消息，人工智能技术在各个领域的应用正在快速发展。专家预测，未来五年内，AI技术将在医疗、教育、交通等多个行业实现重大突破。这些技术的应用不仅将提高工作效率，还将为人们的生活带来更多便利。相关企业正在加大研发投入，推动技术创新和产业升级。",
          },
          {
            id: 10,
            title: "社会新闻",
            content:
              "今天上午，市政府举行了新闻发布会，宣布了一系列惠民政策。这些政策涵盖了教育、医疗、住房等多个方面，旨在提高市民的生活质量。政府表示，将全力推进各项政策的落实，确保广大市民能够真正受益。市民对这些政策表示欢迎，期待早日见到实际效果。",
          },
        ],
      },
    ],

    // 当前显示的文案
    currentText: {},
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.initRecorderManager();
    this.initAudioContext();
    this.switchTab({ currentTarget: { dataset: { index: 0 } } });
  },

  /**
   * 初始化录音管理器
   */
  initRecorderManager() {
    const recorderManager = wx.getRecorderManager();

    recorderManager.onStart(() => {
      console.log("录音开始");
      this.setData({
        isRecording: true,
        recordingStartTime: Date.now(),
      });
      this.startRecordingTimer();
    });

    recorderManager.onStop((res) => {
      console.log("录音结束", res);
      this.setData({
        isRecording: false,
        hasRecording: true,
        currentRecordingPath: res.tempFilePath,
      });
      this.stopRecordingTimer();
    });

    recorderManager.onError((res) => {
      console.error("录音错误:", res);
      wx.showToast({
        title: "录音失败",
        icon: "none",
      });
      this.setData({ isRecording: false });
      this.stopRecordingTimer();
    });

    this.setData({ recorderManager });
  },

  /**
   * 初始化音频上下文
   */
  initAudioContext() {
    const audioContext = wx.createInnerAudioContext();

    audioContext.onPlay(() => {
      this.setData({ isPlaying: true });
    });

    audioContext.onPause(() => {
      this.setData({ isPlaying: false });
    });

    audioContext.onStop(() => {
      this.setData({ isPlaying: false });
    });

    audioContext.onEnded(() => {
      this.setData({ isPlaying: false });
    });

    audioContext.onError((res) => {
      console.error("音频播放错误:", res);
      wx.showToast({
        title: "播放失败",
        icon: "none",
      });
      this.setData({ isPlaying: false });
    });

    this.setData({ audioContext });
  },

  /**
   * 切换tab
   */
  switchTab(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    const category = this.data.textCategories[index];
    const currentText = category.texts[0] || {};

    this.setData({
      activeTab: index,
      currentText: currentText,
      // 切换tab时重置录音状态
      hasRecording: false,
      isRecording: false,
      isPlaying: false,
      recordingTime: "00:00",
      currentRecordingPath: "",
    });

    // 停止当前播放
    if (this.data.audioContext) {
      this.data.audioContext.stop();
    }
  },

  /**
   * 切换录音状态
   */
  toggleRecording() {
    const { isRecording, recorderManager } = this.data;

    if (isRecording) {
      recorderManager.stop();
    } else {
      this.startRecording();
    }
  },

  /**
   * 开始录制
   */
  startRecording() {
    const { recorderManager } = this.data;

    // 请求录音权限
    wx.getSetting({
      success: (res) => {
        if (!res.authSetting["scope.record"]) {
          wx.authorize({
            scope: "scope.record",
            success: () => {
              this.doStartRecording();
            },
            fail: () => {
              wx.showModal({
                title: "需要录音权限",
                content: "请允许录音权限以使用录制功能",
                showCancel: false,
              });
            },
          });
        } else {
          this.doStartRecording();
        }
      },
    });
  },

  /**
   * 执行录制
   */
  doStartRecording() {
    const { recorderManager } = this.data;

    recorderManager.start({
      duration: 60000, // 最长1分钟
      sampleRate: 44100,
      numberOfChannels: 1,
      encodeBitRate: 192000,
      format: "mp3",
    });
  },

  /**
   * 播放录制的音频
   */
  playRecording() {
    const { audioContext, currentRecordingPath, isPlaying } = this.data;

    if (!currentRecordingPath) {
      wx.showToast({
        title: "没有录制内容",
        icon: "none",
      });
      return;
    }

    if (isPlaying) {
      audioContext.pause();
    } else {
      audioContext.src = currentRecordingPath;
      audioContext.play();
    }
  },

  /**
   * 清除录制
   */
  clearRecording() {
    this.setData({
      hasRecording: false,
      recordingTime: "00:00",
      currentRecordingPath: "",
    });
  },

  /**
   * 保存录制
   */
  saveRecording() {
    const { currentRecordingPath, recordingTime, currentText } = this.data;

    if (!currentRecordingPath) {
      wx.showToast({
        title: "没有录制内容",
        icon: "none",
      });
      return;
    }

    wx.showToast({
      title: "保存成功",
      icon: "success",
    });

    // 这里可以添加保存到服务器的逻辑
    console.log("保存录音:", {
      textTitle: currentText.title,
      audioPath: currentRecordingPath,
      duration: recordingTime,
    });
  },

  /**
   * 开始录音计时器
   */
  startRecordingTimer() {
    const timer = setInterval(() => {
      const elapsed = Date.now() - this.data.recordingStartTime;
      const minutes = Math.floor(elapsed / 60000);
      const seconds = Math.floor((elapsed % 60000) / 1000);
      const timeString = `${minutes.toString().padStart(2, "0")}:${seconds
        .toString()
        .padStart(2, "0")}`;

      this.setData({
        recordingTime: timeString,
      });
    }, 1000);

    this.setData({ recordingTimer: timer });
  },

  /**
   * 停止录音计时器
   */
  stopRecordingTimer() {
    if (this.data.recordingTimer) {
      clearInterval(this.data.recordingTimer);
      this.setData({ recordingTimer: null });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // 暂停音频播放
    if (this.data.audioContext && this.data.isPlaying) {
      this.data.audioContext.pause();
    }

    // 停止录音
    if (this.data.isRecording && this.data.recorderManager) {
      this.data.recorderManager.stop();
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 清理音频上下文
    if (this.data.audioContext) {
      this.data.audioContext.destroy();
    }

    // 清理录音计时器
    this.stopRecordingTimer();

    // 停止录音
    if (this.data.recorderManager) {
      this.data.recorderManager.stop();
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: "音频录制",
      path: "/pages/CopyAudio/index",
    };
  },
});
