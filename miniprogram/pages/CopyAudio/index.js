// pages/CopyAudio/index.js
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 选中的文案索引
    selectedIndex: -1,

    // 录音相关状态
    isRecording: false,
    hasRecording: false,
    isPlaying: false,
    recordingTime: "00:00",
    recordingDuration: "00:00",
    recordingQuality: "高质量",
    recordingTimer: null,
    recordingStartTime: 0,

    // 录音管理器和音频上下文
    recorderManager: null,
    audioContext: null,
    currentRecordingPath: "",

    // 完成计数
    completedCount: 0,

    // 情感文案数据
    emotionalTexts: [
      {
        id: 1,
        emotion: "happy",
        emotionName: "开心愉悦",
        description: "表达快乐、兴奋的情绪",
        icon: "smile-o",
        text: "今天真是太棒了！阳光明媚，心情特别好，感觉整个世界都在对我微笑。",
        status: "pending", // pending, recording, completed
        audioPath: "",
        duration: "",
      },
      {
        id: 2,
        emotion: "sad",
        emotionName: "悲伤难过",
        description: "表达失落、难过的情绪",
        icon: "smile-comment-o",
        text: "雨滴敲打着窗户，就像我此刻的心情一样沉重，有些事情真的很难忘记。",
        status: "pending",
        audioPath: "",
        duration: "",
      },
      {
        id: 3,
        emotion: "angry",
        emotionName: "愤怒生气",
        description: "表达愤怒、不满的情绪",
        icon: "warning-o",
        text: "这实在是太过分了！我无法容忍这种不公平的对待，必须要说出来！",
        status: "pending",
        audioPath: "",
        duration: "",
      },
      {
        id: 4,
        emotion: "excited",
        emotionName: "激动兴奋",
        description: "表达激动、热情的情绪",
        icon: "fire-o",
        text: "太不可思议了！这个消息让我激动得无法言喻，我要立刻分享给所有人！",
        status: "pending",
        audioPath: "",
        duration: "",
      },
      {
        id: 5,
        emotion: "calm",
        emotionName: "平静温和",
        description: "表达平和、宁静的情绪",
        icon: "flower-o",
        text: "微风轻抚过湖面，一切都显得那么宁静美好，让人感到内心的平静。",
        status: "pending",
        audioPath: "",
        duration: "",
      },
      {
        id: 6,
        emotion: "surprised",
        emotionName: "惊讶意外",
        description: "表达惊讶、意外的情绪",
        icon: "star-o",
        text: "什么？这怎么可能！我完全没有想到会是这样的结果，真是太意外了！",
        status: "pending",
        audioPath: "",
        duration: "",
      },
      {
        id: 7,
        emotion: "gentle",
        emotionName: "温柔体贴",
        description: "表达温柔、关怀的情绪",
        icon: "like-o",
        text: "别担心，一切都会好起来的。我会一直陪在你身边，给你最温暖的支持。",
        status: "pending",
        audioPath: "",
        duration: "",
      },
      {
        id: 8,
        emotion: "serious",
        emotionName: "严肃认真",
        description: "表达严肃、正式的情绪",
        icon: "certificate",
        text: "这是一个非常重要的决定，我们必须慎重考虑所有的因素和可能的后果。",
        status: "pending",
        audioPath: "",
        duration: "",
      },
    ],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.initRecorderManager();
    this.initAudioContext();
    this.updateCompletedCount();
  },

  /**
   * 初始化录音管理器
   */
  initRecorderManager() {
    const recorderManager = wx.getRecorderManager();

    recorderManager.onStart(() => {
      console.log("录音开始");
      this.setData({
        isRecording: true,
        recordingStartTime: Date.now(),
      });
      this.startRecordingTimer();
    });

    recorderManager.onPause(() => {
      console.log("录音暂停");
      this.setData({ isRecording: false });
      this.stopRecordingTimer();
    });

    recorderManager.onResume(() => {
      console.log("录音继续");
      this.setData({
        isRecording: true,
        recordingStartTime: Date.now() - this.getRecordingSeconds() * 1000,
      });
      this.startRecordingTimer();
    });

    recorderManager.onStop((res) => {
      console.log("录音结束", res);
      this.setData({
        isRecording: false,
        hasRecording: true,
        currentRecordingPath: res.tempFilePath,
        recordingDuration: this.data.recordingTime,
        recordingQuality: this.getRecordingQuality(res.duration),
      });
      this.stopRecordingTimer();
    });

    recorderManager.onError((res) => {
      console.error("录音错误:", res);
      wx.showToast({
        title: "录音失败",
        icon: "none",
      });
      this.setData({ isRecording: false });
      this.stopRecordingTimer();
    });

    this.setData({ recorderManager });
  },

  /**
   * 初始化音频上下文
   */
  initAudioContext() {
    const audioContext = wx.createInnerAudioContext();

    audioContext.onPlay(() => {
      this.setData({ isPlaying: true });
    });

    audioContext.onPause(() => {
      this.setData({ isPlaying: false });
    });

    audioContext.onStop(() => {
      this.setData({ isPlaying: false });
    });

    audioContext.onEnded(() => {
      this.setData({ isPlaying: false });
    });

    audioContext.onError((res) => {
      console.error("音频播放错误:", res);
      wx.showToast({
        title: "播放失败",
        icon: "none",
      });
      this.setData({ isPlaying: false });
    });

    this.setData({ audioContext });
  },

  /**
   * 选择文案
   */
  selectText(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    const currentText = this.data.emotionalTexts[index];

    // 如果已经完成录制，允许重新录制
    if (currentText.status === "completed") {
      this.reRecordCompleted(e);
      return;
    }

    this.setData({
      selectedIndex: index,
      hasRecording: false,
      recordingTime: "00:00",
      currentRecordingPath: "",
    });

    // 更新状态为录制中
    this.updateTextStatus(index, "recording");
  },

  /**
   * 取消选择
   */
  cancelSelection() {
    const { selectedIndex } = this.data;
    if (selectedIndex >= 0) {
      this.updateTextStatus(selectedIndex, "pending");
    }

    this.setData({
      selectedIndex: -1,
      hasRecording: false,
      recordingTime: "00:00",
      currentRecordingPath: "",
    });
  },

  /**
   * 开始录制
   */
  startRecording() {
    const { recorderManager } = this.data;

    // 请求录音权限
    wx.getSetting({
      success: (res) => {
        if (!res.authSetting["scope.record"]) {
          wx.authorize({
            scope: "scope.record",
            success: () => {
              this.doStartRecording();
            },
            fail: () => {
              wx.showModal({
                title: "需要录音权限",
                content: "请允许录音权限以使用录制功能",
                showCancel: false,
              });
            },
          });
        } else {
          this.doStartRecording();
        }
      },
    });
  },

  /**
   * 执行录制
   */
  doStartRecording() {
    const { recorderManager } = this.data;

    recorderManager.start({
      duration: 60000, // 最长1分钟
      sampleRate: 44100,
      numberOfChannels: 1,
      encodeBitRate: 192000,
      format: "mp3",
    });
  },

  /**
   * 切换录制状态
   */
  toggleRecording() {
    const { isRecording, recorderManager } = this.data;

    if (isRecording) {
      recorderManager.pause();
    } else {
      if (this.data.hasRecording) {
        recorderManager.resume();
      } else {
        this.startRecording();
      }
    }
  },

  /**
   * 重新录制
   */
  reRecord() {
    this.setData({
      hasRecording: false,
      recordingTime: "00:00",
      currentRecordingPath: "",
    });
  },

  /**
   * 确认录制
   */
  confirmRecording() {
    const { selectedIndex, currentRecordingPath, recordingDuration } =
      this.data;

    if (!currentRecordingPath) {
      wx.showToast({
        title: "请先录制音频",
        icon: "none",
      });
      return;
    }

    // 更新文案状态为已完成
    this.updateTextStatus(selectedIndex, "completed", {
      audioPath: currentRecordingPath,
      duration: recordingDuration,
    });

    // 重置选择状态
    this.setData({
      selectedIndex: -1,
      hasRecording: false,
      recordingTime: "00:00",
      currentRecordingPath: "",
    });

    // 更新完成计数
    this.updateCompletedCount();

    wx.showToast({
      title: "录制完成",
      icon: "success",
    });
  },

  /**
   * 播放录制的音频
   */
  playRecording() {
    const { audioContext, currentRecordingPath, isPlaying } = this.data;

    if (!currentRecordingPath) {
      wx.showToast({
        title: "没有录制内容",
        icon: "none",
      });
      return;
    }

    if (isPlaying) {
      audioContext.pause();
    } else {
      audioContext.src = currentRecordingPath;
      audioContext.play();
    }
  },

  /**
   * 播放已完成的录音
   */
  playCompleted(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    const text = this.data.emotionalTexts[index];
    const { audioContext, isPlaying } = this.data;

    if (!text.audioPath) {
      wx.showToast({
        title: "音频文件不存在",
        icon: "none",
      });
      return;
    }

    if (isPlaying) {
      audioContext.pause();
    } else {
      audioContext.src = text.audioPath;
      audioContext.play();
    }
  },

  /**
   * 重新录制已完成的项目
   */
  reRecordCompleted(e) {
    const index = parseInt(e.currentTarget.dataset.index);

    // 更新状态为录制中
    this.updateTextStatus(index, "recording");

    this.setData({
      selectedIndex: index,
      hasRecording: false,
      recordingTime: "00:00",
      currentRecordingPath: "",
    });
  },

  /**
   * 开始录音计时器
   */
  startRecordingTimer() {
    const timer = setInterval(() => {
      const elapsed = Date.now() - this.data.recordingStartTime;
      const minutes = Math.floor(elapsed / 60000);
      const seconds = Math.floor((elapsed % 60000) / 1000);
      const timeString = `${minutes.toString().padStart(2, "0")}:${seconds
        .toString()
        .padStart(2, "0")}`;

      this.setData({
        recordingTime: timeString,
      });
    }, 1000);

    this.setData({ recordingTimer: timer });
  },

  /**
   * 停止录音计时器
   */
  stopRecordingTimer() {
    if (this.data.recordingTimer) {
      clearInterval(this.data.recordingTimer);
      this.setData({ recordingTimer: null });
    }
  },

  /**
   * 获取录制秒数
   */
  getRecordingSeconds() {
    const timeStr = this.data.recordingTime;
    const [minutes, seconds] = timeStr.split(":").map(Number);
    return minutes * 60 + seconds;
  },

  /**
   * 获取录制质量
   */
  getRecordingQuality(duration) {
    if (duration < 3000) {
      return "时长过短";
    } else if (duration > 30000) {
      return "时长过长";
    } else {
      return "高质量";
    }
  },

  /**
   * 更新文案状态
   */
  updateTextStatus(index, status, extraData = {}) {
    const emotionalTexts = [...this.data.emotionalTexts];
    emotionalTexts[index] = {
      ...emotionalTexts[index],
      status,
      ...extraData,
    };

    this.setData({ emotionalTexts });
  },

  /**
   * 更新完成计数
   */
  updateCompletedCount() {
    const completedCount = this.data.emotionalTexts.filter(
      (item) => item.status === "completed"
    ).length;
    this.setData({ completedCount });
  },

  /**
   * 保存进度
   */
  saveProgress() {
    const { emotionalTexts, completedCount } = this.data;

    // 这里可以保存到本地存储或服务器
    wx.setStorageSync("emotionalRecordingProgress", {
      texts: emotionalTexts,
      completedCount,
      saveTime: new Date().toISOString(),
    });

    wx.showToast({
      title: "进度已保存",
      icon: "success",
    });
  },

  /**
   * 完成录制
   */
  completeRecording() {
    const { completedCount, emotionalTexts } = this.data;

    if (completedCount < emotionalTexts.length) {
      wx.showToast({
        title: "请完成所有录音",
        icon: "none",
      });
      return;
    }

    wx.showModal({
      title: "录制完成",
      content: `恭喜！您已完成所有${emotionalTexts.length}个情感录音。这些录音将用于训练您的专属声音模型。`,
      confirmText: "确定",
      showCancel: false,
      success: () => {
        // 这里可以上传录音文件到服务器
        this.uploadRecordings();
      },
    });
  },

  /**
   * 上传录音文件
   */
  uploadRecordings() {
    const { emotionalTexts } = this.data;
    const completedTexts = emotionalTexts.filter(
      (item) => item.status === "completed"
    );

    wx.showLoading({
      title: "上传中...",
    });

    // 模拟上传过程
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: "上传成功",
        icon: "success",
      });

      // 返回上一页或跳转到结果页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 2000);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 尝试恢复之前的录制进度
    this.loadProgress();
  },

  /**
   * 加载录制进度
   */
  loadProgress() {
    try {
      const savedProgress = wx.getStorageSync("emotionalRecordingProgress");
      if (savedProgress && savedProgress.texts) {
        this.setData({
          emotionalTexts: savedProgress.texts,
          completedCount: savedProgress.completedCount || 0,
        });

        wx.showToast({
          title: "已恢复录制进度",
          icon: "success",
          duration: 1500,
        });
      }
    } catch (error) {
      console.log("加载进度失败:", error);
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时更新完成计数
    this.updateCompletedCount();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // 暂停音频播放
    if (this.data.audioContext && this.data.isPlaying) {
      this.data.audioContext.pause();
    }

    // 暂停录音
    if (this.data.isRecording && this.data.recorderManager) {
      this.data.recorderManager.pause();
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 清理音频上下文
    if (this.data.audioContext) {
      this.data.audioContext.destroy();
    }

    // 清理录音计时器
    this.stopRecordingTimer();

    // 停止录音
    if (this.data.recorderManager) {
      this.data.recorderManager.stop();
    }

    // 自动保存进度
    this.saveProgress();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 刷新页面状态
    this.updateCompletedCount();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 可以在这里加载更多文案（如果有的话）
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: "情感录音训练",
      path: "/pages/CopyAudio/index",
      imageUrl: "/assets/share-audio.jpg",
    };
  },
});
