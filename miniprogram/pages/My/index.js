// pages/My/index.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    show: false,
    userAvatar: "",
    userName: "用户123456",
    userId: "123456",
    powerValue: "120",
    inputLength: 0,
    tempUserName: "",
    tempUserAvatar: "",
  },

  /**
   * 关闭用户信息编辑弹窗
   */
  onClose() {
    this.setData({
      show: false,
      tempUserName: "",
      tempUserAvatar: "",
    });
  },

  /**
   * 打开用户信息编辑弹窗
   */
  actionUserInfo() {
    this.setData({
      show: true,
      tempUserName: this.data.userName,
      tempUserAvatar: this.data.userAvatar,
      inputLength: this.data.userName ? this.data.userName.length : 0,
    });
  },

  /**
   * 监听昵称输入
   */
  onInputNickname(e) {
    const value = e.detail.value;
    this.setData({
      tempUserName: value,
      inputLength: value.length,
    });
  },

  /**
   * 选择头像
   */
  onChooseAvatar() {
    wx.chooseImage({
      count: 1,
      sizeType: ["compressed"],
      sourceType: ["album", "camera"],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.setData({
          tempUserAvatar: tempFilePath,
        });
      },
    });
  },

  /**
   * 保存用户信息
   */
  onSaveProfile() {
    // 在实际应用中，这里应该有上传头像和保存用户信息的API调用
    this.setData({
      userName: this.data.tempUserName || this.data.userName,
      userAvatar: this.data.tempUserAvatar || this.data.userAvatar,
      show: false,
    });

    wx.showToast({
      title: "保存成功",
      icon: "success",
      duration: 2000,
    });
  },

  /**
   * 购买算力
   */
  onBuyPower() {
    wx.showToast({
      title: "购买功能即将上线",
      icon: "none",
    });
  },

  /**
   * 查看使用记录
   */
  onViewHistory() {
    wx.showToast({
      title: "记录功能即将上线",
      icon: "none",
    });
  },

  /**
   * 兑换算力
   */
  onExchangePower() {
    wx.showToast({
      title: "兑换功能即将上线",
      icon: "none",
    });
  },

  /**
   * 我的算力
   */
  onMyPower() {
    wx.showToast({
      title: "算力详情即将上线",
      icon: "none",
    });
  },

  /**
   * 新手教程
   */
  onTutorial() {
    wx.showToast({
      title: "教程功能即将上线",
      icon: "none",
    });
  },

  /**
   * 联系我们
   */
  onContactUs() {
    wx.showToast({
      title: "联系功能即将上线",
      icon: "none",
    });
  },

  /**
   * 关于
   */
  onAbout() {
    wx.showToast({
      title: "关于页面即将上线",
      icon: "none",
    });
  },

  /**
   * 设置
   */
  onSettings() {
    wx.showToast({
      title: "设置功能即将上线",
      icon: "none",
    });
  },

  /**
   * 意见反馈
   */
  onFeedback() {
    wx.showToast({
      title: "反馈功能即将上线",
      icon: "none",
    });
  },

  /**
   * 帮助中心
   */
  onHelp() {
    wx.showToast({
      title: "帮助中心即将上线",
      icon: "none",
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 在实际应用中，这里应该获取用户信息
    // 模拟获取用户数据
    // setTimeout(() => {
    //   this.setData({
    //     userName: "用户123456",
    //     userId: "23456754",
    //     powerValue: "120",
    //   });
    // }, 500);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    const userInfo = wx.getStorageSync('userInfo') ?? {};
    this.setData({
      powerValue: userInfo.tokens,
      userName: userInfo.name,
      userId: userInfo.open_id.substr(0, 6),
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 模拟刷新数据
    // setTimeout(() => {
    //   this.setData({
    //     powerValue: Math.floor(Math.random() * 200 + 100).toString(),
    //   });
    //   wx.stopPullDownRefresh();
    // }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: "我的个人中心",
      path: "/pages/My/index",
    };
  },
});