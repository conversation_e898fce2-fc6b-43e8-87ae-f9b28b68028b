// pages/CreateAudioVideo/index.js
const app = getApp();

const {
  PostData
} = require("../../common/server");
const {
  formatDuration
} = require("../../utils/utils");

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // Audio source selection
    audioSource: "", // 'select' or 'record'
    selectedAudio: null,
    isPlaying: false,

    // Audio recording
    isRecording: false,
    hasRecording: false,
    recordingTime: "00:00",
    recordingTimer: null,
    recordingStartTime: 0,
    recorderManager: null,
    recordingPath: "",

    // Audio playback
    audioContext: null,

    // Video playback
    videoContext: null,
    playingVideoId: "",

    // Audio speed control
    playbackSpeed: 1,
    adjustedDuration: "00:00",

    // Audio list
    audioList: [],
    isLoadingAudio: false,
    audioError: "",

    // Video model selection
    selectedModel: null,
    modelList: [],
    isLoadingVideo: false,
    videoError: "",

    // Generation process
    isGenerating: false,
    generationProgress: 0,
    generationStatus: "准备中...",
    generatedVideo: null,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.initAudioContext();
    this.initVideoContext();
    this.initRecorderManager();
    this.getAudioList();
    this.getVideoList();
  },

  /**
   * 初始化音频上下文
   */
  initAudioContext() {
    const audioContext = wx.createInnerAudioContext();

    audioContext.onPlay(() => {
      this.setData({
        isPlaying: true
      });
    });

    audioContext.onPause(() => {
      this.setData({
        isPlaying: false
      });
    });

    audioContext.onStop(() => {
      this.setData({
        isPlaying: false
      });
    });

    audioContext.onEnded(() => {
      this.setData({
        isPlaying: false
      });
    });

    audioContext.onError((res) => {
      console.error("音频播放错误:", res);
      wx.showToast({
        title: "音频播放失败",
        icon: "none",
      });
    });

    this.setData({
      audioContext
    });
  },

  /**
   * 初始化视频上下文
   */
  initVideoContext() {
    const videoContext = wx.createInnerAudioContext();

    videoContext.onPlay(() => {
      console.log("Video audio started playing");
    });

    videoContext.onPause(() => {
      this.setData({
        playingVideoId: ""
      });
    });

    videoContext.onStop(() => {
      this.setData({
        playingVideoId: ""
      });
    });

    videoContext.onEnded(() => {
      this.setData({
        playingVideoId: ""
      });
    });

    videoContext.onError((res) => {
      console.error("Video audio error:", res);
      this.setData({
        playingVideoId: ""
      });
      wx.showToast({
        title: "视频音频播放失败",
        icon: "none",
      });
    });

    this.setData({
      videoContext
    });
  },

  /**
   * 初始化录音管理器
   */
  initRecorderManager() {
    const recorderManager = wx.getRecorderManager();

    recorderManager.onStart(() => {
      console.log("录音开始");
      this.setData({
        isRecording: true,
        recordingStartTime: Date.now(),
      });
      this.startRecordingTimer();
    });

    recorderManager.onStop((res) => {
      console.log("录音结束", res);
      this.setData({
        isRecording: false,
        hasRecording: true,
        recordingPath: res.tempFilePath,
      });
      this.stopRecordingTimer();
    });

    recorderManager.onError((res) => {
      console.error("录音错误:", res);
      wx.showToast({
        title: "录音失败",
        icon: "none",
      });
      this.setData({
        isRecording: false
      });
      this.stopRecordingTimer();
    });

    this.setData({
      recorderManager
    });
  },

  /**
   * 选择音频源类型
   */
  selectAudioSource(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      audioSource: type,
      selectedAudio: null,
    });
  },

  /**
   * 获取音频列表
   */
  getAudioList() {
    const _this = this;

    this.setData({
      isLoadingAudio: true,
      audioError: "",
    });

    PostData({
        url: "getAudioLst",
        params: {
          open_id: "admin",
          page: 1,
          pageSize: 10000,
        },
      })
      .then((res) => {
        console.log("获取音频列表成功:", res);

        if (res.code === 200) {
          const list =
            res.data?.list?.map((item) => {
              return {
                id: item._id,
                uuid: item.uuid,
                title: item.title,
                duration: formatDuration(item.duration),
                audioUrl: item.oss_url,
                type: item.type || "自定义",
                description: item.description,
                createTime: item.create_at,
                coverUrl: item.cover,
              };
            }) || [];

          _this.setData({
            audioList: list,
            isLoadingAudio: false,
          });
        } else {
          throw new Error(res.message || "获取音频列表失败");
        }
      })
      .catch((error) => {
        console.error("获取音频列表失败:", error);

        _this.setData({
          isLoadingAudio: false,
          audioError: error.message || "网络错误，请稍后重试",
        });

        wx.showToast({
          title: error.message || "获取音频列表失败",
          icon: "none",
          duration: 2000,
        });
      });
  },

  /**
   * 获取视频列表
   */
  getVideoList() {
    const _this = this;

    this.setData({
      isLoadingVideo: true,
      videoError: "",
    });

    PostData({
        url: "getVideoList",
        params: {
          open_id: "admin",
          page: 1,
          pageSize: 10000,
        },
      })
      .then((res) => {
        console.log("获取视频列表成功:", res);

        if (res.code === 200) {
          const list =
            res.data?.list?.map((item) => {
              return {
                id: item._id,
                uuid: item.uuid,
                name: item.title,
                title: item.title,
                duration: formatDuration(item.duration),
                videoUrl: item.oss_url,
                coverUrl: item.cover,
                description: item.description || "视频模型",
                createTime: item.create_at,
              };
            }) || [];

          _this.setData({
            modelList: list,
            isLoadingVideo: false,
          });
        } else {
          throw new Error(res.message || "获取视频列表失败");
        }
      })
      .catch((error) => {
        console.error("获取视频列表失败:", error);

        _this.setData({
          isLoadingVideo: false,
          videoError: error.message || "网络错误，请稍后重试",
        });

        wx.showToast({
          title: error.message || "获取视频列表失败",
          icon: "none",
          duration: 2000,
        });
      });
  },

  /**
   * 重新加载音频列表
   */
  reloadAudioList() {
    this.getAudioList();
  },

  /**
   * 重新加载视频列表
   */
  reloadVideoList() {
    this.getVideoList();
  },

  /**
   * 选择音频文件
   */
  selectAudio(e) {
    const audio = e.currentTarget.dataset.audio;
    this.setData({
      selectedAudio: audio,
    });
    this.calculateAdjustedDuration();
  },

  /**
   * 更换音频
   */
  changeAudio() {
    this.setData({
      selectedAudio: null,
      audioSource: "",
    });
  },

  /**
   * 上传音频
   */
  uploadAudio() {
    wx.navigateTo({
      url: "/pages/AddMedia/index?type=audio",
    });
  },

  /**
   * 切换音频播放状态
   */
  toggleAudioPlay() {
    const {
      audioContext,
      selectedAudio,
      isPlaying,
      playbackSpeed
    } = this.data;

    if (!selectedAudio || !selectedAudio.audioUrl) {
      wx.showToast({
        title: "请先选择音频",
        icon: "none",
      });
      return;
    }

    if (isPlaying) {
      audioContext.pause();
    } else {
      audioContext.src = selectedAudio.audioUrl;
      audioContext.playbackRate = playbackSpeed;
      audioContext.play();
    }
  },

  /**
   * 开始录音计时器
   */
  startRecordingTimer() {
    const timer = setInterval(() => {
      const elapsed = Date.now() - this.data.recordingStartTime;
      const minutes = Math.floor(elapsed / 60000);
      const seconds = Math.floor((elapsed % 60000) / 1000);
      const timeString = `${minutes.toString().padStart(2, "0")}:${seconds
        .toString()
        .padStart(2, "0")}`;

      this.setData({
        recordingTime: timeString,
      });
    }, 1000);

    this.setData({
      recordingTimer: timer
    });
  },

  /**
   * 停止录音计时器
   */
  stopRecordingTimer() {
    if (this.data.recordingTimer) {
      clearInterval(this.data.recordingTimer);
      this.setData({
        recordingTimer: null
      });
    }
  },

  /**
   * 切换录音状态
   */
  toggleRecording() {
    const {
      isRecording,
      recorderManager
    } = this.data;

    if (isRecording) {
      recorderManager.pause();
    } else {
      if (this.data.hasRecording) {
        recorderManager.resume();
      } else {
        recorderManager.start({
          duration: 600000, // 最长10分钟
          sampleRate: 44100,
          numberOfChannels: 1,
          encodeBitRate: 192000,
          format: "mp3",
        });
      }
    }
  },

  /**
   * 取消录音
   */
  cancelRecording() {
    const {
      recorderManager
    } = this.data;
    recorderManager.stop();
    this.setData({
      hasRecording: false,
      recordingTime: "00:00",
      recordingPath: "",
    });
  },

  /**
   * 完成录音
   */
  finishRecording() {
    const {
      recordingPath,
      recordingTime
    } = this.data;

    if (!recordingPath) {
      wx.showToast({
        title: "请先录制音频",
        icon: "none",
      });
      return;
    }

    // 创建录音音频对象
    const recordedAudio = {
      id: Date.now(),
      title: `录制音频_${new Date().toLocaleTimeString()}`,
      duration: recordingTime,
      type: "录制",
      audioUrl: recordingPath,
      name: `录制音频_${recordingTime}`,
    };

    this.setData({
      selectedAudio: recordedAudio,
    });

    this.calculateAdjustedDuration();
  },

  /**
   * 设置播放倍速
   */
  setPlaybackSpeed(e) {
    const speed = parseFloat(e.currentTarget.dataset.speed);
    this.setData({
      playbackSpeed: speed,
    });
    this.calculateAdjustedDuration();

    // 如果正在播放，更新播放速度（保持原音频和音色）
    if (this.data.isPlaying && this.data.audioContext) {
      // 使用原始音频源，只改变播放速度
      const {
        selectedAudio
      } = this.data;
      if (selectedAudio && selectedAudio.audioUrl) {
        this.data.audioContext.src = selectedAudio.audioUrl;
        this.data.audioContext.playbackRate = speed;
        // 重新播放以应用新的速度设置
        this.data.audioContext.play();
      }
    }
  },

  /**
   * 倍速滑块变化
   */
  onSpeedSliderChange(e) {
    const speed = parseFloat(e.detail.value);
    this.setData({
      playbackSpeed: speed,
    });
    this.calculateAdjustedDuration();

    // 如果正在播放，实时更新播放速度（保持原音频和音色）
    if (this.data.isPlaying && this.data.audioContext) {
      const {
        selectedAudio
      } = this.data;
      if (selectedAudio && selectedAudio.audioUrl) {
        this.data.audioContext.playbackRate = speed;
      }
    }
  },

  /**
   * 计算调整后的时长
   */
  calculateAdjustedDuration() {
    const {
      selectedAudio,
      playbackSpeed
    } = this.data;

    if (!selectedAudio || !selectedAudio.duration) {
      this.setData({
        adjustedDuration: "00:00"
      });
      return;
    }

    // 解析原始时长
    const [minutes, seconds] = selectedAudio.duration.split(":").map(Number);
    const totalSeconds = minutes * 60 + seconds;

    // 计算调整后的时长
    const adjustedSeconds = Math.round(totalSeconds / playbackSpeed);
    const adjustedMinutes = Math.floor(adjustedSeconds / 60);
    const remainingSeconds = adjustedSeconds % 60;

    const adjustedDuration = `${adjustedMinutes
      .toString()
      .padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;

    this.setData({
      adjustedDuration
    });
  },

  /**
   * 预览倍速效果（保持原音频和音色）
   */
  previewSpeed() {
    const {
      audioContext,
      selectedAudio,
      playbackSpeed
    } = this.data;

    if (!selectedAudio || !selectedAudio.audioUrl) {
      wx.showToast({
        title: "请先选择音频",
        icon: "none",
      });
      return;
    }

    // 使用原始音频源，确保音色不变
    audioContext.src = selectedAudio.audioUrl;
    audioContext.playbackRate = playbackSpeed;
    audioContext.play();

    wx.showToast({
      title: `预览 ${playbackSpeed}x 速度`,
      icon: "none",
      duration: 1000,
    });

    // 3秒后自动停止预览
    setTimeout(() => {
      audioContext.stop();
    }, 3000);
  },

  /**
   * 选择视频模型
   */
  selectModel(e) {
    const model = e.currentTarget.dataset.model;
    this.setData({
      selectedModel: model,
    });
  },

  /**
   * 播放视频模型预览
   */
  playVideoModel(e) {
    // e.stopPropagation(); // 阻止事件冒泡，避免触发选择模型

    const model = e.currentTarget.dataset.model;
    const {
      videoContext,
      playingVideoId
    } = this.data;

    if (!model || !model.videoUrl) {
      wx.showToast({
        title: "视频模型无效",
        icon: "none",
      });
      return;
    }

    // 预览视频
    wx.previewMedia({
      sources: [{
        url: model.videoUrl,
        type: "video",
      }, ],
      current: 0,
      success: () => {},
      fail: (err) => {
        console.error("预览视频失败:", err);
        wx.showToast({
          title: "预览视频失败",
          icon: "none",
        });
      },
      complete: () => {
        // 预览结束后重置播放状态
      },
    });

    // 如果当前正在播放同一个视频，则停止播放
    // if (playingVideoId === model.id) {
    //   videoContext.stop();
    //   this.setData({
    //     playingVideoId: ""
    //   });
    //   return;
    // }

    // // 停止之前的播放
    // if (playingVideoId) {
    //   videoContext.stop();
    // }

    // // 开始播放新的视频音频
    // this.setData({
    //   playingVideoId: model.id
    // });

    // videoContext.src = model.videoUrl;
    // videoContext.play();

    // 显示播放提示
    // wx.showToast({
    //   title: "播放视频音频",
    //   icon: "none",
    //   duration: 1000,
    // });
  },

  /**
   * 停止视频播放
   */
  stopVideoPlay() {
    const {
      videoContext
    } = this.data;
    if (videoContext) {
      videoContext.stop();
    }
    this.setData({
      playingVideoId: ""
    });
  },

  /**
   * 上传视频模型
   */
  uploadModel() {
    wx.navigateTo({
      url: "/pages/AddMedia/index?type=video",
    });
  },

  /**
   * 生成视频
   */
  generateVideo() {
    const {
      selectedAudio,
      selectedModel,
      playbackSpeed
    } = this.data;

    if (!selectedAudio) {
      wx.showToast({
        title: "请先选择音频",
        icon: "none",
      });
      return;
    }

    if (!selectedModel) {
      wx.showToast({
        title: "请先选择视频模型",
        icon: "none",
      });
      return;
    }

    this.setData({
      isGenerating: true,
      generationProgress: 0,
      generationStatus: "准备中...",
    });
    const params = {
      
    }
    // 模拟生成过程
    this.simulateGeneration();
  },

  /**
   * 模拟视频生成过程
   */
  simulateGeneration() {
    const steps = [{
        progress: 10,
        status: "分析音频内容..."
      },
      {
        progress: 25,
        status: "加载视频模型..."
      },
      {
        progress: 40,
        status: "生成唇形同步..."
      },
      {
        progress: 60,
        status: "渲染视频帧..."
      },
      {
        progress: 80,
        status: "合成音视频..."
      },
      {
        progress: 95,
        status: "优化输出质量..."
      },
      {
        progress: 100,
        status: "生成完成！"
      },
    ];

    let currentStep = 0;

    const updateProgress = () => {
      if (currentStep < steps.length) {
        const step = steps[currentStep];
        this.setData({
          generationProgress: step.progress,
          generationStatus: step.status,
        });

        currentStep++;
        setTimeout(updateProgress, 2000 + Math.random() * 1000);
      } else {
        // 生成完成
        this.setData({
          isGenerating: false,
          generatedVideo: {
            url: "https://wxsnsdy.tc.qq.com/105/20210/snsdyvideodownload?filekey=30280201010421301f0201690402534804102ca905ce620b1241b726bc41dcff44e00204012882540400&bizid=1023&hy=SH&fileparam=302c020101042530230204136ffd93020457e3c4ff02024ef202031e8d7f02030f42400204045a320a0201000400",
            poster: "https://img.yzcdn.cn/vant/cat.jpeg",
            duration: this.data.adjustedDuration,
            size: "12.5MB",
          },
        });
      }
    };

    updateProgress();
  },

  /**
   * 重新生成
   */
  regenerate() {
    this.setData({
      generatedVideo: null,
    });
    this.generateVideo();
  },

  /**
   * 保存视频
   */
  saveVideo() {
    wx.showToast({
      title: "视频保存成功",
      icon: "success",
    });

    // 可以在这里添加保存到相册的逻辑
    setTimeout(() => {
      wx.navigateBack();
    }, 1500);
  },

  /**
   * 重置所有设置
   */
  reset() {
    wx.showModal({
      title: "确认重置",
      content: "重置后将清空所有设置，确定继续吗？",
      success: (res) => {
        if (res.confirm) {
          this.setData({
            audioSource: "",
            selectedAudio: null,
            selectedModel: null,
            playbackSpeed: 1,
            isRecording: false,
            hasRecording: false,
            recordingTime: "00:00",
            recordingPath: "",
            generatedVideo: null,
          });

          // 停止音频播放
          if (this.data.audioContext) {
            this.data.audioContext.stop();
          }

          // 停止录音
          if (this.data.isRecording && this.data.recorderManager) {
            this.data.recorderManager.stop();
          }
        }
      },
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // 暂停音频播放
    if (this.data.audioContext && this.data.isPlaying) {
      this.data.audioContext.pause();
    }

    // 暂停录音
    if (this.data.isRecording && this.data.recorderManager) {
      this.data.recorderManager.pause();
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 清理音频上下文
    if (this.data.audioContext) {
      this.data.audioContext.destroy();
    }

    // 清理视频上下文
    if (this.data.videoContext) {
      this.data.videoContext.destroy();
    }

    // 清理录音计时器
    this.stopRecordingTimer();

    // 停止录音
    if (this.data.recorderManager) {
      this.data.recorderManager.stop();
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 刷新音频列表和模型列表
    this.filterAudio();
    this.filterModels();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: "音视频创作工具",
      path: "/pages/CreateAudioVideo/index",
    };
  },
});