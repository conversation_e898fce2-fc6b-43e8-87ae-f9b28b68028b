<view class="container">
  <!-- Header -->
  <view class="header">
    <view class="header-title">音视频创作</view>
    <view class="header-subtitle">选择音频源和视频模型，创建专属内容</view>
  </view>

  <!-- Main Content -->
  <scroll-view scroll-y class="content" enhanced show-scrollbar="{{false}}">

    <!-- Step 1: Audio Source Selection -->
    <view class="step-section">
      <view class="step-header">
        <view class="step-number">1</view>
        <view class="step-info">
          <view class="step-title">选择音频源</view>
          <view class="step-desc">选择已有音频或录制新音频</view>
        </view>
      </view>

      <view class="audio-source-options">
        <view class="source-option {{audioSource === 'select' ? 'active' : ''}}" bindtap="selectAudioSource" data-type="select">
          <view class="option-icon">
            <van-icon name="music-o" size="32rpx" color="{{audioSource === 'select' ? '#fff' : '#FF7F11'}}" />
          </view>
          <view class="option-content">
            <view class="option-title">选择音频</view>
            <view class="option-desc">从音频库中选择</view>
          </view>
        </view>

        <view class="source-option {{audioSource === 'record' ? 'active' : ''}}" bindtap="selectAudioSource" data-type="record">
          <view class="option-icon">
            <van-icon name="volume-o" size="32rpx" color="{{audioSource === 'record' ? '#fff' : '#FF7F11'}}" />
          </view>
          <view class="option-content">
            <view class="option-title">录制音频</view>
            <view class="option-desc">实时录制新音频</view>
          </view>
        </view>
      </view>

      <!-- Selected Audio Display -->
      <view class="selected-audio" wx:if="{{selectedAudio}}">
        <view class="audio-info">
          <view class="audio-avatar">
            <van-icon name="music-o" size="24rpx" color="#fff" />
          </view>
          <view class="audio-details">
            <view class="audio-name">{{selectedAudio.title || selectedAudio.name}}</view>
            <view class="audio-meta">
              <text>{{selectedAudio.duration || '00:00'}}</text>
              <text class="separator">•</text>
              <text>{{selectedAudio.type || '音频文件'}}</text>
            </view>
          </view>
        </view>
        <view class="audio-actions">
          <view class="action-btn play-btn" bindtap="toggleAudioPlay">
            <van-icon name="{{isPlaying ? 'pause-circle-o' : 'play-circle-o'}}" size="32rpx" color="#FF7F11" />
          </view>
          <view class="action-btn change-btn" bindtap="changeAudio">
            <van-icon name="exchange" size="24rpx" color="#666" />
          </view>
        </view>
      </view>

      <!-- Audio Selection Interface -->
      <view class="audio-selection" wx:if="{{audioSource === 'select' && !selectedAudio}}">
        <view class="selection-header">
          <view class="selection-title">选择音频文件</view>
          <view class="filter-tabs">
            <view class="filter-tab {{audioFilter === 'all' ? 'active' : ''}}" bindtap="filterAudio" data-filter="all">全部</view>
            <view class="filter-tab {{audioFilter === 'uploaded' ? 'active' : ''}}" bindtap="filterAudio" data-filter="uploaded">已上传</view>
            <view class="filter-tab {{audioFilter === 'system' ? 'active' : ''}}" bindtap="filterAudio" data-filter="system">系统</view>
          </view>
        </view>

        <view class="audio-list">
          <view class="audio-item" wx:for="{{filteredAudioList}}" wx:key="id" bindtap="selectAudio" data-audio="{{item}}">
            <view class="item-avatar">
              <van-icon name="music-o" size="20rpx" color="#fff" />
            </view>
            <view class="item-info">
              <view class="item-title">{{item.title}}</view>
              <view class="item-meta">{{item.duration}} • {{item.type}}</view>
            </view>
            <view class="item-action">
              <van-icon name="play-circle-o" size="28rpx" color="#FF7F11" />
            </view>
          </view>
        </view>

        <view class="upload-option" bindtap="uploadAudio">
          <van-icon name="plus" size="24rpx" color="#FF7F11" />
          <text>上传新音频</text>
        </view>
      </view>

      <!-- Audio Recording Interface -->
      <view class="audio-recording" wx:if="{{audioSource === 'record' && !selectedAudio}}">
        <view class="recording-area">
          <view class="recording-visual {{isRecording ? 'recording' : ''}}">
            <view class="wave-container">
              <view class="wave-circle wave1"></view>
              <view class="wave-circle wave2"></view>
              <view class="wave-circle wave3"></view>
            </view>
            <view class="record-icon">
              <van-icon name="{{isRecording ? 'pause-circle-o' : 'play-circle-o'}}" size="48rpx" color="#fff" />
            </view>
          </view>

          <view class="recording-info">
            <view class="recording-time">{{recordingTime}}</view>
            <view class="recording-status">{{isRecording ? '正在录制...' : '点击开始录制'}}</view>
          </view>
        </view>

        <view class="recording-controls">
          <button class="control-btn secondary" bindtap="cancelRecording" disabled="{{!hasRecording}}">
            <van-icon name="delete-o" size="20rpx" />
            <text>取消</text>
          </button>

          <button class="control-btn primary" bindtap="toggleRecording">
            <van-icon name="{{isRecording ? 'pause-circle-o' : 'play-circle-o'}}" size="20rpx" />
            <text>{{isRecording ? '暂停' : (hasRecording ? '继续' : '开始')}}</text>
          </button>

          <button class="control-btn success" bindtap="finishRecording" disabled="{{!hasRecording}}">
            <van-icon name="success" size="20rpx" />
            <text>完成</text>
          </button>
        </view>
      </view>
    </view>

    <!-- Step 2: Audio Speed Control -->
    <view class="step-section" wx:if="{{selectedAudio}}">
      <view class="step-header">
        <view class="step-number">2</view>
        <view class="step-info">
          <view class="step-title">音频倍速</view>
          <view class="step-desc">调整音频播放速度</view>
        </view>
      </view>

      <view class="speed-control">
        <view class="speed-options">
          <view class="speed-option {{playbackSpeed === 0.5 ? 'active' : ''}}" bindtap="setPlaybackSpeed" data-speed="0.5">
            <text>0.5x</text>
          </view>
          <view class="speed-option {{playbackSpeed === 0.75 ? 'active' : ''}}" bindtap="setPlaybackSpeed" data-speed="0.75">
            <text>0.75x</text>
          </view>
          <view class="speed-option {{playbackSpeed === 1 ? 'active' : ''}}" bindtap="setPlaybackSpeed" data-speed="1">
            <text>1x</text>
          </view>
          <view class="speed-option {{playbackSpeed === 1.25 ? 'active' : ''}}" bindtap="setPlaybackSpeed" data-speed="1.25">
            <text>1.25x</text>
          </view>
          <view class="speed-option {{playbackSpeed === 1.5 ? 'active' : ''}}" bindtap="setPlaybackSpeed" data-speed="1.5">
            <text>1.5x</text>
          </view>
          <view class="speed-option {{playbackSpeed === 2 ? 'active' : ''}}" bindtap="setPlaybackSpeed" data-speed="2">
            <text>2x</text>
          </view>
        </view>

        <view class="speed-slider">
          <view class="slider-label">自定义倍速</view>
          <slider class="custom-slider" min="0.5" max="2" step="0.1" value="{{playbackSpeed}}" bindchange="onSpeedSliderChange" activeColor="#FF7F11" backgroundColor="#f0f0f0" block-color="#FF7F11" block-size="20" />
          <view class="slider-value">{{playbackSpeed}}x</view>
        </view>

        <view class="speed-preview">
          <view class="preview-info">
            <text>预览效果：</text>
            <text class="preview-duration">预计时长 {{adjustedDuration}}</text>
          </view>
          <button class="preview-btn" bindtap="previewSpeed">
            <van-icon name="play-circle-o" size="16rpx" />
            <text>试听</text>
          </button>
        </view>
      </view>
    </view>

    <!-- Step 3: Video Model Selection -->
    <view class="step-section" wx:if="{{selectedAudio}}">
      <view class="step-header">
        <view class="step-number">3</view>
        <view class="step-info">
          <view class="step-title">选择视频模型</view>
          <view class="step-desc">选择数字人模型生成视频</view>
        </view>
      </view>

      <view class="video-model-selection">
        <view class="model-categories">
          <view class="category-tab {{modelCategory === 'all' ? 'active' : ''}}" bindtap="filterModels" data-category="all">全部</view>
          <view class="category-tab {{modelCategory === 'business' ? 'active' : ''}}" bindtap="filterModels" data-category="business">商务</view>
          <view class="category-tab {{modelCategory === 'casual' ? 'active' : ''}}" bindtap="filterModels" data-category="casual">休闲</view>
          <view class="category-tab {{modelCategory === 'professional' ? 'active' : ''}}" bindtap="filterModels" data-category="professional">专业</view>
        </view>

        <view class="model-grid">
          <view class="model-item {{selectedModel && selectedModel.id === item.id ? 'selected' : ''}}" wx:for="{{filteredModelList}}" wx:key="id" bindtap="selectModel" data-model="{{item}}">
            <view class="model-cover">
              <image src="{{item.coverUrl}}" mode="aspectFill" />
              <view class="model-play-overlay">
                <van-icon name="play-circle-o" size="32rpx" color="#fff" />
              </view>
              <view class="model-tag {{item.category}}">{{item.categoryName}}</view>
            </view>
            <view class="model-info">
              <view class="model-name">{{item.name}}</view>
              <view class="model-desc">{{item.description}}</view>
            </view>
          </view>
        </view>

        <view class="upload-model-option" bindtap="uploadModel">
          <van-icon name="plus" size="24rpx" color="#FF7F11" />
          <text>上传自定义模型</text>
        </view>
      </view>
    </view>

    <!-- Generation Progress -->
    <view class="generation-section" wx:if="{{isGenerating}}">
      <view class="progress-container">
        <view class="progress-icon">
          <van-loading color="#FF7F11" size="32rpx" />
        </view>
        <view class="progress-info">
          <view class="progress-title">正在生成视频...</view>
          <view class="progress-desc">{{generationStatus}}</view>
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{generationProgress}}%"></view>
          </view>
          <view class="progress-text">{{generationProgress}}%</view>
        </view>
      </view>
    </view>

    <!-- Result Preview -->
    <view class="result-section" wx:if="{{generatedVideo}}">
      <view class="result-header">
        <view class="result-title">生成完成</view>
        <view class="result-actions">
          <button class="action-btn secondary" bindtap="regenerate">重新生成</button>
          <button class="action-btn primary" bindtap="saveVideo">保存视频</button>
        </view>
      </view>

      <view class="video-preview">
        <video class="preview-video" src="{{generatedVideo.url}}" poster="{{generatedVideo.poster}}" controls show-center-play-btn show-play-btn />
        <view class="video-info">
          <view class="video-meta">
            <text>时长: {{generatedVideo.duration}}</text>
            <text class="separator">•</text>
            <text>大小: {{generatedVideo.size}}</text>
          </view>
        </view>
      </view>
    </view>

  </scroll-view>

  <!-- Bottom Actions -->
  <view class="bottom-actions" wx:if="{{!isGenerating && !generatedVideo}}">
    <button class="action-button secondary" bindtap="reset">重置</button>
    <button class="action-button primary" bindtap="generateVideo" disabled="{{!selectedAudio || !selectedModel}}">
      生成视频
    </button>
  </view>
</view>