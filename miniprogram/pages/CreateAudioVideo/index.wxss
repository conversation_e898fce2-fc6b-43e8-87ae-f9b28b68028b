/* pages/CreateAudioVideo/index.wxss */

/* Global styles */
page {
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  height: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  box-sizing: border-box;
}

/* Header styles */
.header {
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  padding: 40rpx 30rpx;
  color: #fff;
  box-shadow: 0 2rpx 12rpx rgba(255, 127, 17, 0.2);
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 26rpx;
  opacity: 0.9;
}

/* Content area */
.content {
  flex: 1;
  padding: 30rpx;
  padding-bottom: 140rpx;
  /* Space for bottom actions */
}

/* Step section styling */
.step-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.step-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.step-number {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 600;
  margin-right: 20rpx;
}

.step-info {
  flex: 1;
}

.step-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 6rpx;
}

.step-desc {
  font-size: 26rpx;
  color: #666;
}

/* Audio source options */
.audio-source-options {
  display: flex;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.source-option {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 24rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.source-option.active {
  border-color: #ff7f11;
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  color: #fff;
}

.option-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: rgba(255, 127, 17, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.source-option.active .option-icon {
  background-color: rgba(255, 255, 255, 0.2);
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.option-desc {
  font-size: 24rpx;
  opacity: 0.8;
}

/* Selected audio display */
.selected-audio {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  margin-bottom: 24rpx;
}

.audio-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.audio-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.audio-details {
  flex: 1;
}

.audio-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 6rpx;
}

.audio-meta {
  font-size: 24rpx;
  color: #666;
}

.separator {
  margin: 0 8rpx;
}

.audio-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.95);
}

.play-btn {
  background-color: rgba(255, 127, 17, 0.1);
}

.change-btn {
  background-color: #f0f0f0;
}

/* Audio selection interface */
.audio-selection {
  margin-top: 24rpx;
}

.selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.selection-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.filter-tabs {
  display: flex;
  gap: 8rpx;
}

.filter-tab {
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  color: #666;
  background-color: #f0f0f0;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background-color: #ff7f11;
  color: #fff;
}

.audio-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.audio-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.audio-item:last-child {
  border-bottom: none;
}

.item-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.item-info {
  flex: 1;
}

.item-title {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.item-meta {
  font-size: 22rpx;
  color: #999;
}

.item-action {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-option {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  margin-top: 16rpx;
  border: 2rpx dashed #ff7f11;
  border-radius: 12rpx;
  color: #ff7f11;
  font-size: 26rpx;
  gap: 12rpx;
  transition: all 0.3s ease;
}

.upload-option:active {
  background-color: rgba(255, 127, 17, 0.05);
}

/* Audio recording interface */
.audio-recording {
  margin-top: 24rpx;
}

.recording-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 16rpx;
  margin-bottom: 32rpx;
}

.recording-visual {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
}

.wave-container {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wave-circle {
  position: absolute;
  border: 2rpx solid rgba(255, 127, 17, 0.3);
  border-radius: 50%;
  opacity: 0;
}

.recording-visual.recording .wave-circle {
  animation: wave-pulse 2s infinite;
}

.wave1 {
  width: 120rpx;
  height: 120rpx;
  animation-delay: 0s;
}

.wave2 {
  width: 160rpx;
  height: 160rpx;
  animation-delay: 0.5s;
}

.wave3 {
  width: 200rpx;
  height: 200rpx;
  animation-delay: 1s;
}

@keyframes wave-pulse {
  0% {
    transform: scale(0.5);
    opacity: 1;
  }

  100% {
    transform: scale(1);
    opacity: 0;
  }
}

.record-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(255, 127, 17, 0.3);
  z-index: 2;
}

.recording-info {
  text-align: center;
}

.recording-time {
  font-size: 48rpx;
  font-weight: 600;
  color: #ff7f11;
  margin-bottom: 8rpx;
}

.recording-status {
  font-size: 26rpx;
  color: #666;
}

.recording-controls {
  display: flex;
  justify-content: center;
  gap: 24rpx;
}

.control-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  border-radius: 32rpx;
  font-size: 26rpx;
  border: none;
  transition: all 0.3s ease;
}

.control-btn.secondary {
  background-color: #f8f9fa;
  color: #666;
}

.control-btn.primary {
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  color: #fff;
}

.control-btn.success {
  background-color: #28a745;
  color: #fff;
}

.control-btn[disabled] {
  opacity: 0.5;
  pointer-events: none;
}

/* Speed control */
.speed-control {
  margin-top: 24rpx;
}

.speed-options {
  display: flex;
  gap: 12rpx;
  margin-bottom: 32rpx;
  flex-wrap: wrap;
}

.speed-option {
  flex: 1;
  min-width: 80rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #f0f0f0;
  border-radius: 32rpx;
  background-color: #fafafa;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.speed-option.active {
  border-color: #ff7f11;
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  color: #fff;
}

.speed-slider {
  margin-bottom: 24rpx;
}

.slider-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.custom-slider {
  margin-bottom: 16rpx;
}

.slider-value {
  text-align: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #ff7f11;
}

.speed-preview {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.preview-info {
  flex: 1;
}

.preview-duration {
  color: #ff7f11;
  font-weight: 500;
  margin-left: 8rpx;
}

.preview-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background-color: #ff7f11;
  color: #fff;
  border-radius: 24rpx;
  font-size: 24rpx;
  border: none;
}

/* Video model selection */
.video-model-selection {
  margin-top: 24rpx;
}

.model-categories {
  display: flex;
  gap: 12rpx;
  margin-bottom: 24rpx;
  overflow-x: auto;
  padding-bottom: 8rpx;
}

.category-tab {
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  color: #666;
  background-color: #f0f0f0;
  border-radius: 24rpx;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.category-tab.active {
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  color: #fff;
}

.model-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.model-item {
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.model-item.selected {
  border-color: #ff7f11;
  box-shadow: 0 4rpx 16rpx rgba(255, 127, 17, 0.2);
}

.model-cover {
  position: relative;
  width: 100%;
  aspect-ratio: 9/16;
  overflow: hidden;
}

.model-cover image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.model-play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.model-item:active .model-play-overlay {
  opacity: 1;
}

.model-tag {
  position: absolute;
  top: 12rpx;
  left: 12rpx;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.6);
}

.model-info {
  padding: 16rpx;
}

.model-name {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 6rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.model-desc {
  font-size: 22rpx;
  color: #999;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.upload-model-option {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
  border: 2rpx dashed #ff7f11;
  border-radius: 12rpx;
  color: #ff7f11;
  font-size: 26rpx;
  gap: 12rpx;
  transition: all 0.3s ease;
}

.upload-model-option:active {
  background-color: rgba(255, 127, 17, 0.05);
}

/* Generation progress */
.generation-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.progress-icon {
  flex-shrink: 0;
}

.progress-info {
  flex: 1;
}

.progress-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.progress-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background-color: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff7f11, #ff5500);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: right;
  font-size: 24rpx;
  color: #ff7f11;
  font-weight: 500;
}

/* Result section */
.result-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.result-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.result-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  padding: 12rpx 20rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.secondary {
  background-color: #f8f9fa;
  color: #666;
}

.action-btn.primary {
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  color: #fff;
}

.video-preview {
  border-radius: 12rpx;
  overflow: hidden;
}

.preview-video {
  width: 100%;
  height: 400rpx;
  background-color: #000;
}

.video-info {
  padding: 16rpx;
  background-color: #f8f9fa;
}

.video-meta {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* Bottom actions */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 16rpx;
  padding: 20rpx 30rpx calc(20rpx + env(safe-area-inset-bottom));
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.action-button {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  transition: all 0.3s ease;
}

.action-button.secondary {
  background-color: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
}

.action-button.primary {
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(255, 127, 17, 0.2);
}

.action-button[disabled] {
  background-color: #f0f0f0;
  color: #ccc;
  box-shadow: none;
  pointer-events: none;
}

.action-button:active {
  transform: scale(0.98);
}

/* Responsive adjustments */
@media (max-width: 750rpx) {
  .model-grid {
    grid-template-columns: 1fr;
  }

  .speed-options {
    grid-template-columns: repeat(3, 1fr);
  }

  .audio-source-options {
    flex-direction: column;
  }
}