// pages/Inspiration/index.js
const {
  GetData,
  wssData
} = require("../../common/server");
const app = getApp();
Page({
  /**
   * 页面的初始数据
   */
  data: {
    socket: null,
    // 输入内容
    write: "",
    // 字符串数据
    selectVal: "",
    selectTopicArr: [],
    showResult: 1, // 1: initial, 2: loading, 3: result
    number: "3", // 3，6，10
    // 结合人设
    useIpSetting: false,
    ipSeting: {},
    selectedTopic: {},
    checked: true,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {},
  checkSelectEvent(e) {
    const index = e.currentTarget.dataset.index;
    const item = this.data.selectTopicArr[index];
    const selectTopicArr = this.data.selectTopicArr.map((item, i) => {
      return {
        ...item,
        checked: index === i ? true : false,
      };
    });
    this.setData({
      selectedTopic: item,
      selectTopicArr,
      checked: false,
    });
  },
  createSelectTitle() {
    let selectData = "";
    const scope = this;
    const socket = wssData({
      url: "wssSelectTopic",
    });
    scope.setData({
      showResult: 2,
      selectTopicArr: [],
      checked: true,
    });
    socket.onOpen(() => {
      scope.setData({
        socket: socket,
      });

      // 判断输入内容
      if (!this.data.write || this.data.write.trim().length === 0) {
        wx.showToast({
          title: "请输入主题内容",
          icon: "none",
        });
        return;
      }
      const params = {
        open_id: "admin",
        content: this.data.write,
        number: this.data.number,
        type: "selectTopic",
      };
      // 判断是否使用人设
      if (this.data.useIpSetting) {
        params.ipSetting = this.data.ipSeting.params;
      }
      // uuid
      if (this.data.uuid) {
        params.uuid = this.data.uuid;
      }

      socket.send({
        data: JSON.stringify(params),
        success() {
          scope.setData({
            showResult: 2,
          });
          console.log("消息发送成功");
        },
        fail(err) {
          scope.setData({
            showResult: 1,
          });
          wx.showToast({
            title: "发送失败，请重试",
            icon: "none",
          });
          console.error("消息发送失败", err);
        },
      });
      console.log("websocket 已经连接成功");
    });
    socket.onClose(() => {
      if (scope.data.showResult === 2) {
        scope.setData({
          showResult: 3,
        });
      }
      console.log("websocket 已经关闭连接");
    });
    socket.onMessage((res) => {
      const data = JSON.parse(res.data);
      if (data.status === "message") {
        selectData += `${data.message}`;
      } else if (data.status === "finish") {
        console.log(selectData);
        try {
          const arr = JSON.parse(selectData);
          const st = arr.map((item, index) => {
            return {
              checked: false,
              ...item,
            };
          });
          this.setData({
            selectVal: selectData,
            selectTopicArr: st,
            showResult: 3,
            uuid: data.message,
          });
        } catch (e) {
          console.error("解析结果失败:", e);
          this.setData({
            showResult: 1,
          });
          wx.showToast({
            title: "结果解析失败，请重试",
            icon: "none",
          });
        }
        socket.close();
      } else if (data.status === "error") {
        this.setData({
          showResult: 1,
        });
        wx.showToast({
          title: "生成失败，请重试",
          icon: "none",
        });
        socket.close();
      }
    });
  },
  closeSelectTitle() {
    if (this.data.socket) {
      this.data.socket.close(1000, "前端主动关闭");
    }
  },
  /**
   * 选择创作类型
   */
  selectCreationType(e) {
    const number = e.currentTarget.dataset.number;
    this.setData({
      number: number,
    });
  },

  /**
   * 切换人设开关
   */
  toggleIpSetting(e) {
    this.setData({
      useIpSetting: e.detail,
    });
  },

  /**
   * 更换人设
   */
  changeIpSetting() {
    wx.navigateTo({
      url: "/pages/IPListSelect/index",
    });
  },

  /**
   * 复制结果
   */
  // copyResult() {
  //   if (this.data.selectVal) {
  //     wx.setClipboardData({
  //       data: this.data.selectVal,
  //       success: () => {
  //         wx.showToast({
  //           title: "复制成功",
  //           icon: "success",
  //         });
  //       },
  //     });
  //   }
  // },

  /**
   * 跳转到创作页面
   */
  gotoUrl() {
    wx.navigateTo({
      url: "/pages/CreateTitle/index",
    });
  },

  /**
   * 输入内容更新
   */
  writeChangeHandler(e) {
    let value = e.detail.value;
    if (value.length > 1000) {
      value = value.substring(0, 1000);

      wx.showToast({
        title: "最多输入1000字",
        icon: "none",
        duration: 1500,
      });
    }
    this.setData({
      write: value,
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    const defaultIpSetting = wx.getStorageSync("selectIpSetting");
    if (defaultIpSetting && defaultIpSetting.checked) {
      const value = defaultIpSetting.checked;
      this.setData({
        ipSeting: value,
      });
      delete defaultIpSetting.checked;
      wx.setStorageSync("selectIpSetting", defaultIpSetting);
    } else if (defaultIpSetting && defaultIpSetting.default) {
      const value = defaultIpSetting.default;
      this.setData({
        ipSeting: value,
      });
    } else {
      this.setData({
        ipSeting: {
          params: {
            ip_name: "",
          },
        },
      });
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
});