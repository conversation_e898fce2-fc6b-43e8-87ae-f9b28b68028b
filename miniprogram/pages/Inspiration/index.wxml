<view class="container">
  <!-- Header -->
  <view class="header">
    <view class="header-title">灵感创作</view>
    <view class="header-subtitle">根据主题信息生成创作灵感和选题方向</view>
  </view>

  <!-- Main Content -->
  <view class="content">
    <!-- Input Section -->
    <view class="section">
      <view class="section-header">
        <view class="section-title">
          <text class="required">*</text>
          <text>主题内容</text>
        </view>
        <view class="character-count {{write.length > 900 ? 'near-limit' : ''}}">
          {{write.length}}/1000
        </view>
      </view>
      <cs-text-area bind:valuechange="writeChangeHandler" maxValue="1000" height="320" placeholderTxt="请输入或粘贴要创作的主题内容" />
    </view>

    <!-- Options Section -->
    <view class="section">
      <view class="section-header">
        <view class="section-title">创作设置</view>
      </view>

      <view class="style-options">
        <view class="style-option-group">
          <view class="option-label">创作选题</view>
          <view class="style-buttons">
            <view class="style-btn {{creationType === 'topic' ? 'active' : ''}}" data-type="topic" bindtap="selectCreationType">选题</view>
            <view class="style-btn {{creationType === 'outline' ? 'active' : ''}}" data-type="outline" bindtap="selectCreationType">大纲</view>
            <view class="style-btn {{creationType === 'idea' ? 'active' : ''}}" data-type="idea" bindtap="selectCreationType">创意</view>
          </view>
        </view>

        <view class="style-option-group">
          <view class="option-label">结合人设</view>
          <van-switch checked="{{useIpSetting}}" active-color="#FF7F11" size="24px" bind:change="toggleIpSetting" />
        </view>
      </view>

      <!-- IP Setting Card (only shown when useIpSetting is true) -->
      <view class="ip-setting-card" wx:if="{{useIpSetting}}">
        <view class="ip-info">
          <view class="ip-title">电商类型直播卖货的人设设定</view>
          <view class="ip-description">激情昂扬信息作为，设定更加便宜，女性在家里没设么事ing</view>
        </view>
        <view class="ip-action">
          <button class="change-ip-btn" bindtap="changeIpSetting">更换</button>
        </view>
      </view>
    </view>

    <!-- Results Section (only shown after generation) -->
    <view class="section result-section" wx:if="{{showResult > 1}}">
      <view class="section-header">
        <view class="section-title">创作结果</view>
        <!-- <view class="result-actions" wx:if="{{showResult === 3}}">
          <view class="action-button small" bindtap="copyResult">
            <van-icon name="description" size="14px" />
            <text>复制</text>
          </view>
        </view> -->
      </view>

      <view class="result-container">
        <!-- Loading State -->
        <view class="result-loading" wx:if="{{selectTopicArr.length <= 0}}">
          <van-loading color="#FF7F11" size="36px" />
          <text class="loading-text">AI正在创作中...</text>
        </view>

        <!-- Result Content -->
        <view class="result-content" wx:else>
          <view wx:for="{{selectTopicArr}}" wx:key="unique" class="result-item {{item.checked?'result-item-active':''}}" bind:tap="checkSelectEvent" data-index="{{index}}">
            <view class="result-item-title">{{index+1}}. {{item.title}}</view>
            <view class="result-item-description">{{item.because}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- Footer Actions -->
  <view class="footer">
    <!-- Initial State: Only Start Button -->
    <block wx:if="{{showResult === 1}}">
      <button class="primary-button" bindtap="createSelectTitle" disabled="{{!write || write.trim().length === 0}}">
        开始创作
      </button>
    </block>

    <!-- Loading State -->
    <block wx:elif="{{showResult === 2}}">
      <button class="primary-button loading">
        <van-loading color="#FFFFFF" size="20px" />
        <text>创作中...</text>
      </button>
    </block>

    <!-- Result State: Regenerate and Create Buttons -->
    <block wx:elif="{{showResult === 3}}">
      <button class="secondary-button" bindtap="createSelectTitle">
        重新生成
      </button>
      <button class="primary-button" bindtap="gotoUrl">
        开始创作
      </button>
    </block>
  </view>
</view>