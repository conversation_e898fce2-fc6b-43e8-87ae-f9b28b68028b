/* pages/Inspiration/index.wxss */

/* Global styles */
page {
  background-color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  height: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding-bottom: 140rpx;
  /* Space for the footer */
  box-sizing: border-box;
}

/* Header styles */
.header {
  background-color: #ff7f11;
  padding: 40rpx 30rpx;
  color: #fff;
}

.header-title {
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 26rpx;
  opacity: 0.9;
}

/* Main content */
.content {
  flex: 1;
  padding: 30rpx;
}

/* Section styling */
.section {
  background-color: #fff;
  border-radius: 0;
  padding: 30rpx;
  margin-bottom: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.required {
  color: #ff4d4f;
  margin-right: 6rpx;
}

.character-count {
  font-size: 24rpx;
  color: #999;
}

.character-count.near-limit {
  color: #ff7f11;
}

/* Text input area */
.text-area-container {
  border: 1rpx solid #f0f0f0;
  border-radius: 0;
  background-color: #fafafa;
  padding: 20rpx;
  margin-bottom: 16rpx;
}

/* Style options */
.style-options {
  margin-bottom: 24rpx;
}

.style-option-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.style-option-group:last-child {
  margin-bottom: 0;
}

.option-label {
  font-size: 28rpx;
  color: #666;
}

.style-buttons {
  display: flex;
  gap: 16rpx;
}

.style-btn {
  padding: 12rpx 24rpx;
  background-color: #f5f5f5;
  color: #666;
  border-radius: 0;
  font-size: 26rpx;
  transition: all 0.3s;
}

.style-btn.active {
  background-color: #ff7f11;
  color: #fff;
}

/* IP Setting Card */
.ip-setting-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fafafa;
  border-radius: 0;
  padding: 20rpx;
  margin-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
}

.ip-info {
  flex: 1;
  margin-right: 20rpx;
}

.ip-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.ip-description {
  font-size: 24rpx;
  color: #999;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.ip-action {
  flex-shrink: 0;
}

.change-ip-btn {
  font-size: 24rpx;
  padding: 8rpx 20rpx;
  background-color: transparent;
  color: #ff7f11;
  border: 1rpx solid #ff7f11;
  border-radius: 0;
  line-height: 1.5;
  margin: 0;
}

/* Results section */
.result-section {
  background-color: #fafafa;
  border-top: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
}

.result-actions {
  display: flex;
  gap: 16rpx;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background-color: #f5f5f5;
  color: #666;
  border-radius: 0;
  font-size: 24rpx;
  border: 1rpx solid #f0f0f0;
}

.action-button.small {
  padding: 6rpx 12rpx;
  font-size: 22rpx;
}

.result-container {
  min-height: 200rpx;
}

.result-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  gap: 24rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.result-content {
  padding: 20rpx 0;
}

.result-item {
  margin-bottom: 24rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 0;
  border: 1rpx solid #f0f0f0;
}
.result-item-active {
  border: 1rpx solid var(--color);
  color: var(--color);
}

.result-item:last-child {
  margin-bottom: 0;
}

.result-item-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
  line-height: 1.5;
}

.result-item-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* Footer */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 20rpx 30rpx calc(20rpx + env(safe-area-inset-bottom));
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
}

.primary-button,
.secondary-button {
  flex: 1;
  height: 80rpx;
  border-radius: 0;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 10rpx;
  border: none;
  gap: 12rpx;
}

.primary-button {
  background-color: #ff7f11;
  color: #fff;
}

.primary-button[disabled] {
  background-color: #ffd0a8;
  color: #fff;
}

.primary-button.loading {
  background-color: #ff7f11;
  color: #fff;
}

.secondary-button {
  background-color: #fff;
  color: #666;
  border: 1rpx solid #f0f0f0;
}
