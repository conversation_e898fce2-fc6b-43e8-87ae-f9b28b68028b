// pages/Workflow/index.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    activeTab: 0, // 当前激活的标签页索引
    showTopicOnly: false, // 是否只显示选题内容

    // 人设定位数据
    gender: "男", // 默认男性
    age: 22, // 默认年龄22

    // 账号设定数据
    targetAgeGroups: [], // 目标受众年龄多选
    targetGender: "男", // 目标受众性别，默认男
    ageGroupOptions: [
      { value: "18-24", label: "18-24岁" },
      { value: "25-30", label: "25-30岁" },
      { value: "31-35", label: "31-35岁" },
      { value: "36-40", label: "36-40岁" },
      { value: "41-45", label: "41-45岁" },
      { value: "46-50", label: "46-50岁" },
      { value: "50+", label: "50岁以上" },
    ],
    targetGenderOptions: [
      { value: "男", label: "男" },
      { value: "女", label: "女" },
      { value: "男女不限", label: "男女不限" },
    ],

    // 内容定位数据
    videoStyle: "", // 短视频风格
    videoStyleOptions: [
      { value: "轻松、幽默", label: "轻松、幽默" },
      { value: "严肃", label: "严肃" },
      { value: "专业", label: "专业" },
    ],
  },

  /**
   * 切换到选题标签页
   */
  switchToTopic() {
    this.setData({
      activeTab: 1, // 选题标签页的索引为1
      showTopicOnly: true,
    });
  },

  /**
   * 切换到全部标签页
   */
  switchToAll() {
    this.setData({
      activeTab: 0, // 全部标签页的索引为0
      showTopicOnly: false,
    });
  },

  /**
   * 性别选择事件
   */
  onGenderChange(e) {
    this.setData({
      gender: e.currentTarget.dataset.value,
    });
  },

  /**
   * 年龄滑块变化事件
   */
  onAgeChange(e) {
    this.setData({
      age: e.detail.value,
    });
  },

  /**
   * 目标受众年龄组选择事件
   */
  onTargetAgeGroupChange(e) {
    const value = e.currentTarget.dataset.value;
    let targetAgeGroups = [...this.data.targetAgeGroups];

    const index = targetAgeGroups.indexOf(value);
    if (index > -1) {
      targetAgeGroups.splice(index, 1);
    } else {
      targetAgeGroups.push(value);
    }

    this.setData({
      targetAgeGroups,
    });
  },

  /**
   * 目标受众性别选择事件
   */
  onTargetGenderChange(e) {
    this.setData({
      targetGender: e.currentTarget.dataset.value,
    });
  },

  /**
   * 视频风格选择事件
   */
  onVideoStyleChange(e) {
    this.setData({
      videoStyle: e.currentTarget.dataset.value,
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log(options);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
});
