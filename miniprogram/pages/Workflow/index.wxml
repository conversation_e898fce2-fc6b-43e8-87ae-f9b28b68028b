<view class="page-container">
  <!-- 固定在顶部的标签页导航 -->
  <view class="fixed-tab-area">
    <scroll-view scroll-x class="tab-scroll" enhanced show-scrollbar="{{false}}">
      <view class="tab_main">
        <view class="tab_item {{activeTab === 0 ? 'tab_active' : ''}}" bindtap="switchToAll">
          全部
        </view>
        <view class="tab_item {{activeTab === 1 ? 'tab_active' : ''}}" bindtap="switchToTopic">
          选题
        </view>
        <view class="tab_item">
          文案
        </view>
        <view class="tab_item">
          音频作品
        </view>
        <view class="tab_item">
          视频作品
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 内容区域 - 有顶部内边距以避免被固定标签遮挡 -->
  <view class="content-area">
    <scroll-view scroll-y class="scroll_main" enhanced show-scrollbar="{{false}}">
    <view class="scroll_main_all">

      <!-- 人设定位表单 -->
      <view class="form-section">
        <view class="section-title">人设定位</view>

        <!-- 性别选择 -->
        <view class="form-item">
          <view class="form-label">性别</view>
          <view class="gender-selector">
            <view
              class="gender-option {{gender === '男' ? 'active' : ''}}"
              data-value="男"
              bindtap="onGenderChange"
            >
              <text>男</text>
            </view>
            <view
              class="gender-option {{gender === '女' ? 'active' : ''}}"
              data-value="女"
              bindtap="onGenderChange"
            >
              <text>女</text>
            </view>
          </view>
        </view>

        <!-- 年龄滑块 -->
        <view class="form-item">
          <view class="form-label">年龄: {{age}}岁</view>
          <view class="slider-container">
            <slider
              min="18"
              max="99"
              value="{{age}}"
              bindchange="onAgeChange"
              activeColor="#ff7f11"
              backgroundColor="#f0f0f0"
              block-color="#ff7f11"
              block-size="20"
            />
            <view class="slider-labels">
              <text>18</text>
              <text>99</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 账号设定表单 -->
      <view class="form-section">
        <view class="section-title">账号设定</view>

        <!-- 目标受众年龄多选 -->
        <view class="form-item">
          <view class="form-label">目标受众年龄</view>
          <view class="checkbox-group">
            <view
              class="checkbox-item {{targetAgeGroups.indexOf(item.value) > -1 ? 'checked' : ''}}"
              wx:for="{{ageGroupOptions}}"
              wx:key="value"
              data-value="{{item.value}}"
              bindtap="onTargetAgeGroupChange"
            >
              <view class="checkbox-icon">
                <text wx:if="{{targetAgeGroups.indexOf(item.value) > -1}}">✓</text>
              </view>
              <text>{{item.label}}</text>
            </view>
          </view>
        </view>

        <!-- 目标受众性别单选 -->
        <view class="form-item">
          <view class="form-label">目标受众性别</view>
          <view class="radio-group">
            <view
              class="radio-item {{targetGender === item.value ? 'active' : ''}}"
              wx:for="{{targetGenderOptions}}"
              wx:key="value"
              data-value="{{item.value}}"
              bindtap="onTargetGenderChange"
            >
              <view class="radio-icon">
                <view class="radio-dot {{targetGender === item.value ? 'active' : ''}}"></view>
              </view>
              <text>{{item.label}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 内容定位表单 -->
      <view class="form-section">
        <view class="section-title">内容定位</view>

        <!-- 短视频风格单选 -->
        <view class="form-item">
          <view class="form-label">您希望短视频账号是什么样的风格？</view>
          <view class="radio-group">
            <view
              class="radio-item {{videoStyle === item.value ? 'active' : ''}}"
              wx:for="{{videoStyleOptions}}"
              wx:key="value"
              data-value="{{item.value}}"
              bindtap="onVideoStyleChange"
            >
              <view class="radio-icon">
                <view class="radio-dot {{videoStyle === item.value ? 'active' : ''}}"></view>
              </view>
              <text>{{item.label}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 选题专家 -->
      <view class="workflow-card topic-card">
        <view class="card-left">
          <view class="title_pic topic-icon">
            <text>选</text>
          </view>
          <view class="connect-line"></view>
        </view>
        <view class="card-right">
          <view class="card-header">
            <view class="main_title">
              <text>数字人合成的信心视觉体验在哪里？这是一个非常长的标题，需要显示两行并且省略多余的内容</text>
            </view>
            <view class="status-tag completed">
              <view class="status-dot"></view>
              <text>已完成</text>
            </view>
          </view>
          <view class="sub_title">方向：自主武器、AI黑客攻防、数据战与伦理争议</view>
          <view class="card-footer">
            <view class="timestamp">
              <view class="timestamp-icon"></view>
              <text>2023-05-20 10:30</text>
            </view>
            <view class="card-actions">
              <view class="action-btn">
                <view class="action-btn-icon edit-icon"></view>
              </view>
              <view class="action-btn">
                <view class="action-btn-icon share-icon"></view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 音频专家 - 只在全部标签页显示 -->
      <view class="workflow-card audio-card" wx:if="{{!showTopicOnly}}">
        <view class="card-left">
          <view class="title_pic audio-icon">
            <text>音</text>
          </view>
          <view class="connect-line"></view>
        </view>
        <view class="card-right">
          <view class="card-header">
            <view class="main_title">
              <text>数字人合成的信心视觉体验在哪里？这是一个非常长的音频标题，需要显示两行并且省略多余的内容</text>
            </view>
            <view class="status-tag processing">
              <view class="status-dot"></view>
              <text>处理中</text>
            </view>
          </view>
          <view class="audio-container">
            <view class="audio_item">
              <view class="audio_info">
                <view class="audio-duration">
                  <view class="duration-icon"></view>
                  <text>30分42秒</text>
                </view>
                <view class="download-btn">
                  <view class="download-icon"></view>
                  <text>下载</text>
                </view>
              </view>
              <view class="audio-wave-container">
                <view class="audio-wave">
                  <view class="wave-bar" wx:for="{{8}}" wx:key="index" style="height: {{[60, 40, 80, 30, 70, 50, 90, 45][index]}}rpx;"></view>
                </view>
              </view>
              <view class="audio-progress">
                <view class="progress-bar">
                  <view class="progress-inner"></view>
                  <view class="progress-handle"></view>
                </view>
                <view class="progress-time">
                  <text>00:00</text>
                  <text>30:42</text>
                </view>
              </view>
              <view class="audio-controls">
                <view class="control-btn rewind-btn"></view>
                <view class="audio_play">
                  <view class="play-icon"></view>
                </view>
                <view class="control-btn forward-btn"></view>
              </view>
            </view>
          </view>
          <view class="card-footer">
            <view class="timestamp">
              <view class="timestamp-icon"></view>
              <text>2023-05-18 15:45</text>
            </view>
            <view class="card-actions">
              <view class="action-btn">
                <view class="action-btn-icon edit-icon"></view>
              </view>
              <view class="action-btn">
                <view class="action-btn-icon share-icon"></view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 文案专家 - 只在全部标签页显示 -->
      <view class="workflow-card doc-card" wx:if="{{!showTopicOnly}}">
        <view class="card-left">
          <view class="title_pic doc-icon">
            <text>文</text>
          </view>
          <view class="connect-line"></view>
        </view>
        <view class="card-right">
          <view class="card-header">
            <view class="main_title">
              <text>AI技术在现代医疗领域的应用与挑战</text>
            </view>
            <view class="status-tag completed">
              <view class="status-dot"></view>
              <text>已完成</text>
            </view>
          </view>
          <view class="sub_title">方向：医疗诊断、个性化治疗、医疗资源优化</view>
          <view class="doc-preview">
            <view class="doc-header">
              <view class="doc-icon-small"></view>
              <view class="doc-title">文档预览</view>
            </view>
            <view class="doc-content">
              <view class="doc-line"></view>
              <view class="doc-line"></view>
              <view class="doc-line"></view>
              <view class="doc-line"></view>
            </view>
            <view class="doc-footer">
              <view class="doc-pages">共 5 页</view>
              <view class="doc-view-btn">查看全文</view>
            </view>
          </view>
          <view class="card-footer">
            <view class="timestamp">
              <view class="timestamp-icon"></view>
              <text>2023-05-15 09:20</text>
            </view>
            <view class="card-actions">
              <view class="action-btn">
                <view class="action-btn-icon edit-icon"></view>
              </view>
              <view class="action-btn">
                <view class="action-btn-icon share-icon"></view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 视频作品 - 只在全部标签页显示 -->
      <view class="workflow-card video-card" wx:if="{{!showTopicOnly}}">
        <view class="card-left">
          <view class="title_pic video-icon">
            <text>视</text>
          </view>
          <view class="connect-line"></view>
        </view>
        <view class="card-right">
          <view class="card-header">
            <view class="main_title">
              <text>元宇宙与未来社交网络的发展趋势</text>
            </view>
            <view class="status-tag processing">
              <view class="status-dot"></view>
              <text>处理中</text>
            </view>
          </view>
          <view class="video-container">
            <view class="video-preview">
              <image src="https://img.yzcdn.cn/vant/cat.jpeg" mode="aspectFill"></image>
              <view class="video-overlay"></view>
              <view class="video-duration">05:32</view>
              <view class="video-play-btn"></view>
            </view>
            <view class="video-info">
              <view class="video-stats">
                <view class="video-stat-item">
                  <view class="video-stat-icon view-icon"></view>
                  <text>1.2k</text>
                </view>
                <view class="video-stat-item">
                  <view class="video-stat-icon like-icon"></view>
                  <text>86</text>
                </view>
              </view>
              <view class="video-quality">1080p</view>
            </view>
          </view>
          <view class="card-footer">
            <view class="timestamp">
              <view class="timestamp-icon"></view>
              <text>2023-05-10 14:15</text>
            </view>
            <view class="card-actions">
              <view class="action-btn">
                <view class="action-btn-icon edit-icon"></view>
              </view>
              <view class="action-btn">
                <view class="action-btn-icon share-icon"></view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态提示 - 当选题标签页没有内容时显示 -->
      <view class="empty-state" wx:if="{{showTopicOnly && activeTab === 1}}">
        <view class="empty-icon"></view>
        <view class="empty-text">暂无更多选题</view>
        <view class="empty-desc">您可以点击"全部"查看所有内容</view>
      </view>
    </view>

    <!-- 底部间距 -->
    <view class="bottom-space"></view>
  </scroll-view>
  </view>
</view>