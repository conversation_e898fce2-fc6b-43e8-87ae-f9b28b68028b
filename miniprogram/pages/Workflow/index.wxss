/* pages/Workflow/index.wxss */
page {
  height: 100%;
  background-color: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  color: #333;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}

/* Page container */
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

/* Fixed tab area */
.fixed-tab-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 0 0 16rpx;
  z-index: 100;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* Content area */
.content-area {
  flex: 1;
  padding-top: 100rpx; /* Adjust based on the height of your tab area */
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.tab-scroll {
  padding: 0 32rpx;
  width: 100%;
  box-sizing: border-box;
}

.tab_main {
  display: flex;
  padding: 16rpx 0;
  width: max-content;
}

.tab_item {
  display: flex;
  font-size: 28rpx;
  height: 64rpx;
  padding: 0 28rpx;
  margin-right: 24rpx;
  align-items: center;
  justify-content: center;
  color: #666;
  background-color: #f0f2f5;
  border-radius: 32rpx;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.tab_item:last-child {
  margin-right: 0;
}

.tab_item:active {
  opacity: 0.7;
}

.tab_active {
  background-color: #ff7f11;
  color: #fff;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(255, 127, 17, 0.2);
}

/* Main content area */
.scroll_main {
  height: 100%;
  padding: 24rpx 32rpx;
  box-sizing: border-box;
  overflow-y: auto;
}

.scroll_main_all {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* Form section styling */
.form-section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #ff7f11;
  position: relative;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -2rpx;
  left: 0;
  width: 60rpx;
  height: 2rpx;
  background: linear-gradient(90deg, #ff7f11, #ff5500);
}

.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

/* Gender selector styling */
.gender-selector {
  display: flex;
  gap: 16rpx;
}

.gender-option {
  flex: 1;
  height: 80rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f9f9f9;
  transition: all 0.3s ease;
  cursor: pointer;
}

.gender-option text {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.gender-option.active {
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  border-color: #ff7f11;
  box-shadow: 0 4rpx 12rpx rgba(255, 127, 17, 0.2);
}

.gender-option.active text {
  color: #fff;
}

/* Slider styling */
.slider-container {
  padding: 16rpx 0;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #999;
}

/* Checkbox group styling */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 24rpx;
  background-color: #f9f9f9;
  transition: all 0.3s ease;
  cursor: pointer;
}

.checkbox-item.checked {
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  border-color: #ff7f11;
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 127, 17, 0.2);
}

.checkbox-icon {
  width: 24rpx;
  height: 24rpx;
  border-radius: 4rpx;
  background-color: #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16rpx;
  color: #fff;
}

.checkbox-item.checked .checkbox-icon {
  background-color: rgba(255, 255, 255, 0.2);
}

.checkbox-item text {
  font-size: 26rpx;
  color: #666;
}

.checkbox-item.checked text {
  color: #fff;
}

/* Radio group styling */
.radio-group {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  background-color: #f9f9f9;
  transition: all 0.3s ease;
  cursor: pointer;
}

.radio-item.active {
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  border-color: #ff7f11;
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 127, 17, 0.2);
}

.radio-icon {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  border: 2rpx solid #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
}

.radio-item.active .radio-icon {
  border-color: #fff;
}

.radio-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: transparent;
  transition: all 0.3s ease;
}

.radio-dot.active {
  background-color: #fff;
}

.radio-item text {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.radio-item.active text {
  color: #fff;
}

/* Workflow card styling */
.workflow-card {
  display: flex;
  gap: 20rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  overflow: hidden;
}

/* Remove the generic left border since we'll add theme-specific ones */

.workflow-card:active {
  transform: translateY(2rpx) scale(0.99);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.card-left {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.connect-line {
  width: 2rpx;
  height: 100%;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.1),
    rgba(0, 0, 0, 0.03)
  );
  margin: 12rpx 0;
  margin-left: 40rpx;
}

.card-right {
  flex: 1;
  min-width: 0; /* Important for text ellipsis to work */
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
  min-height: 80rpx; /* Ensure enough height for two lines of title */
}

.title_pic {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

/* Theme colors for icons and borders */
.topic-icon {
  background: linear-gradient(135deg, #ff7f11, #ff5500);
}

.audio-icon {
  background: linear-gradient(135deg, #5b8ff9, #1a73e8);
}

.doc-icon {
  background: linear-gradient(135deg, #5ad8a6, #00b578);
}

.video-icon {
  background: linear-gradient(135deg, #f6bd16, #f59a23);
}

/* Theme-specific left borders for cards */
.workflow-card.topic-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background: linear-gradient(to bottom, #ff7f11, #ff5500);
  border-top-left-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
}

.workflow-card.audio-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background: linear-gradient(to bottom, #5b8ff9, #1a73e8);
  border-top-left-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
}

.workflow-card.doc-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background: linear-gradient(to bottom, #5ad8a6, #00b578);
  border-top-left-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
}

.workflow-card.video-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background: linear-gradient(to bottom, #f6bd16, #f59a23);
  border-top-left-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
}

.main_title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  margin-right: 16rpx;
}

.main_title > text {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  max-height: 2.8em; /* 2 lines * 1.4 line-height */
}

.sub_title {
  font-size: 28rpx;
  color: #666;
  margin: 8rpx 0 16rpx;
  line-height: 1.4;
}

/* Status tags */
.status-tag {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  background-color: #f5f5f5;
  color: #666;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.status-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background-color: #999;
}

.status-tag.completed {
  background-color: rgba(90, 216, 166, 0.1);
  color: #00b578;
}

.status-tag.completed .status-dot {
  background-color: #00b578;
}

.status-tag.processing {
  background-color: rgba(91, 143, 249, 0.1);
  color: #1a73e8;
}

.status-tag.processing .status-dot {
  background-color: #1a73e8;
}

/* Card footer */
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

.timestamp {
  font-size: 24rpx;
  color: #999;
  display: flex;
  align-items: center;
}

.timestamp-icon {
  display: inline-block;
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
  background-color: #f0f0f0;
  border-radius: 50%;
  position: relative;
}

.timestamp-icon::before {
  content: "";
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background-color: #999;
  top: 8rpx;
  left: 8rpx;
}

.card-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.9);
  background-color: #ebebeb;
}

.action-btn-icon {
  width: 24rpx;
  height: 24rpx;
  position: relative;
}

.edit-icon::before,
.edit-icon::after {
  content: "";
  position: absolute;
  background-color: #999;
}

.edit-icon::before {
  width: 12rpx;
  height: 2rpx;
  top: 16rpx;
  left: 6rpx;
}

.edit-icon::after {
  width: 2rpx;
  height: 12rpx;
  top: 6rpx;
  left: 11rpx;
}

.share-icon::before {
  content: "";
  position: absolute;
  width: 16rpx;
  height: 16rpx;
  border: 2rpx solid #999;
  border-radius: 50%;
  top: 3rpx;
  left: 3rpx;
}

/* Audio item styling */
.audio-container {
  margin: 16rpx 0;
}

.audio_item {
  background-color: #f9f9f9;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.audio_item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4rpx;
  background: linear-gradient(90deg, #5b8ff9, #1a73e8);
}

.audio_info {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.audio-duration,
.download-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.duration-icon,
.download-icon {
  display: inline-block;
  width: 28rpx;
  height: 28rpx;
  background-color: rgba(91, 143, 249, 0.1);
  border-radius: 50%;
  position: relative;
}

.duration-icon::before {
  content: "";
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  border: 2rpx solid #5b8ff9;
  border-radius: 50%;
  top: 6rpx;
  left: 6rpx;
}

.download-icon::before,
.download-icon::after {
  content: "";
  position: absolute;
  background-color: #ff7f11;
}

.download-icon::before {
  width: 2rpx;
  height: 12rpx;
  top: 6rpx;
  left: 13rpx;
}

.download-icon::after {
  width: 10rpx;
  height: 10rpx;
  border-left: 2rpx solid #ff7f11;
  border-bottom: 2rpx solid #ff7f11;
  transform: rotate(-45deg);
  top: 12rpx;
  left: 8rpx;
}

.download-btn {
  color: #ff7f11;
}

/* Audio wave visualization */
.audio-wave-container {
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
}

.audio-wave {
  display: flex;
  align-items: center;
  gap: 6rpx;
  height: 100%;
}

.wave-bar {
  width: 6rpx;
  background: linear-gradient(to bottom, #5b8ff9, #1a73e8);
  border-radius: 3rpx;
}

/* Audio progress bar */
.audio-progress {
  margin: 16rpx 0;
}

.progress-bar {
  height: 6rpx;
  background-color: #e0e0e0;
  border-radius: 3rpx;
  overflow: hidden;
  position: relative;
}

.progress-inner {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 35%;
  background: linear-gradient(90deg, #5b8ff9, #1a73e8);
  border-radius: 3rpx;
}

.progress-handle {
  position: absolute;
  width: 16rpx;
  height: 16rpx;
  background-color: #ffffff;
  border: 2rpx solid #1a73e8;
  border-radius: 50%;
  top: 50%;
  left: 35%;
  transform: translate(-50%, -50%);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.progress-time {
  display: flex;
  justify-content: space-between;
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
}

/* Audio controls */
.audio-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24rpx;
  margin-top: 20rpx;
}

.control-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background-color: rgba(91, 143, 249, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.rewind-btn::before,
.forward-btn::before {
  content: "";
  position: absolute;
  border-style: solid;
  width: 0;
  height: 0;
}

.rewind-btn::before {
  border-width: 8rpx 12rpx 8rpx 0;
  border-color: transparent #5b8ff9 transparent transparent;
  left: 16rpx;
}

.forward-btn::before {
  border-width: 8rpx 0 8rpx 12rpx;
  border-color: transparent transparent transparent #5b8ff9;
  right: 16rpx;
}

.audio_play {
  width: 64rpx;
  height: 64rpx;
  background: linear-gradient(135deg, #5b8ff9, #1a73e8);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  box-shadow: 0 4rpx 12rpx rgba(26, 115, 232, 0.2);
  transition: all 0.3s ease;
}

.audio_play:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 6rpx rgba(26, 115, 232, 0.3);
}

.play-icon {
  display: inline-block;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 12rpx 0 12rpx 20rpx;
  border-color: transparent transparent transparent #ffffff;
  margin-left: 4rpx;
}

/* Document preview */
.doc-preview {
  background-color: #f9f9f9;
  border-radius: 16rpx;
  padding: 20rpx;
  margin: 16rpx 0;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.doc-preview::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4rpx;
  background: linear-gradient(90deg, #5ad8a6, #00b578);
}

.doc-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding-bottom: 12rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.doc-icon-small {
  width: 32rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #5ad8a6, #00b578);
  border-radius: 8rpx;
  margin-right: 12rpx;
  position: relative;
}

.doc-icon-small::before,
.doc-icon-small::after {
  content: "";
  position: absolute;
  background-color: rgba(255, 255, 255, 0.8);
  left: 8rpx;
}

.doc-icon-small::before {
  width: 16rpx;
  height: 2rpx;
  top: 12rpx;
}

.doc-icon-small::after {
  width: 16rpx;
  height: 2rpx;
  top: 18rpx;
}

.doc-title {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.doc-content {
  padding: 8rpx 0;
}

.doc-line {
  height: 16rpx;
  background-color: #e0e0e0;
  border-radius: 8rpx;
  margin-bottom: 12rpx;
}

.doc-line:nth-child(1) {
  width: 100%;
}

.doc-line:nth-child(2) {
  width: 85%;
}

.doc-line:nth-child(3) {
  width: 70%;
}

.doc-line:nth-child(4) {
  width: 90%;
  margin-bottom: 0;
}

.doc-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
  padding-top: 12rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

.doc-pages {
  font-size: 24rpx;
  color: #999;
}

.doc-view-btn {
  font-size: 24rpx;
  color: #00b578;
  padding: 6rpx 16rpx;
  background-color: rgba(90, 216, 166, 0.1);
  border-radius: 20rpx;
}

/* Video container */
.video-container {
  margin: 16rpx 0;
}

/* Video preview */
.video-preview {
  position: relative;
  width: 100%;
  height: 200rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.5));
  z-index: 1;
}

.video-preview image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-duration {
  position: absolute;
  bottom: 12rpx;
  right: 12rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  font-size: 22rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  z-index: 2;
  display: flex;
  align-items: center;
}

.video-duration::before {
  content: "";
  display: inline-block;
  width: 6rpx;
  height: 6rpx;
  background-color: #f6bd16;
  border-radius: 50%;
  margin-right: 6rpx;
}

.video-play-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 72rpx;
  height: 72rpx;
  background: rgba(246, 189, 22, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  z-index: 2;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.video-play-btn::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 55%;
  transform: translate(-50%, -50%);
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 12rpx 0 12rpx 20rpx;
  border-color: transparent transparent transparent #ffffff;
}

.video-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
}

.video-stats {
  display: flex;
  gap: 16rpx;
}

.video-stat-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}

.video-stat-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 6rpx;
  position: relative;
}

.view-icon::before {
  content: "";
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  border: 2rpx solid #666;
  border-radius: 50%;
  top: 6rpx;
  left: 6rpx;
}

.like-icon::before {
  content: "";
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background-color: #666;
  transform: rotate(45deg);
  top: 8rpx;
  left: 8rpx;
}

.video-quality {
  font-size: 22rpx;
  color: #f6bd16;
  padding: 4rpx 12rpx;
  background-color: rgba(246, 189, 22, 0.1);
  border-radius: 16rpx;
}

/* Empty state styling */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  min-height: 400rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  background-color: #f5f5f5;
  border-radius: 50%;
  margin-bottom: 30rpx;
  position: relative;
}

.empty-icon::before,
.empty-icon::after {
  content: "";
  position: absolute;
  background-color: #ddd;
}

.empty-icon::before {
  width: 40rpx;
  height: 4rpx;
  top: 58rpx;
  left: 40rpx;
}

.empty-icon::after {
  width: 4rpx;
  height: 40rpx;
  top: 40rpx;
  left: 58rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
  text-align: center;
}

/* Bottom space */
.bottom-space {
  height: 60rpx;
}
