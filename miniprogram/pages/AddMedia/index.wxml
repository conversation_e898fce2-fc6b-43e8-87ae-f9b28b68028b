<view class="container">
  <!-- Header -->
  <view class="header">
    <view class="header-title">{{mediaType === 'audio' ? '上传音频' : '上传视频'}}</view>
    <view class="header-subtitle">{{mediaType === 'audio' ? '上传音频文件创建音频模型' : '上传视频文件创建视频模型'}}</view>
  </view>
  <!-- Upload Progress (only shown during upload) -->
  <view class="section upload-progress-section" wx:if="{{uploading}}">
    <view class="section-header">
      <view class="section-title">上传进度</view>
    </view>

    <view class="progress-container">
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{uploadProgress}}%"></view>
      </view>
      <view class="progress-text">{{uploadProgress}}%</view>
    </view>
    <view class="progress-status">正在上传{{mediaType === 'audio' ? '音频' : '视频'}}文件...</view>
  </view>

  <!-- Main Content -->
  <view class="content">
    <!-- File Upload Section -->
    <view class="section">
      <view class="section-header">
        <view class="section-title">
          <text class="required">*</text>
          <text>{{mediaType === 'audio' ? '音频文件' : '视频文件'}}</text>
        </view>
      </view>

      <view class="upload-area" bindtap="chooseMedia">
        <view class="upload-content" wx:if="{{!selectedFile}}">
          <view class="upload-icon">
            <van-icon name="{{mediaType === 'audio' ? 'music-o' : 'video-o'}}" size="48px" color="#FF7F11" />
          </view>
          <view class="upload-text">点击选择{{mediaType === 'audio' ? '音频' : '视频'}}文件</view>
          <view class="upload-hint">{{mediaType === 'audio' ? '支持 MP3、WAV、M4A 格式' : '支持 MP4、MOV、AVI 格式'}}</view>
        </view>

        <view class="file-preview" wx:else>
          <view class="file-info">
            <view class="file-icon">
              <van-icon name="{{mediaType === 'audio' ? 'music-o' : 'video-o'}}" size="32px" color="#FF7F11" />
            </view>
            <view class="file-details">
              <view class="file-name">{{selectedFile.name}}</view>
              <view class="file-size">{{selectedFile.size}}</view>
            </view>
          </view>
          <view class="file-actions">
            <button class="change-file-btn" bindtap="chooseMedia">更换文件</button>
          </view>
        </view>
      </view>
    </view>

    <!-- Title Section -->
    <view class="section">
      <view class="section-header">
        <view class="section-title">
          <text class="required">*</text>
          <text>标题</text>
        </view>
        <view class="character-count {{title.length > 45 ? 'near-limit' : ''}}">
          {{title.length}}/50
        </view>
      </view>

      <view class="input-container">
        <input class="title-input" placeholder="请输入{{mediaType === 'audio' ? '音频' : '视频'}}标题" value="{{title}}" maxlength="50" bindinput="onTitleInput" />
      </view>
    </view>

    <!-- Description Section -->
    <view class="section">
      <view class="section-header">
        <view class="section-title">描述</view>
        <view class="character-count {{description.length > 180 ? 'near-limit' : ''}}">
          {{description.length}}/200
        </view>
      </view>

      <view class="textarea-container">
        <textarea class="description-textarea" placeholder="请输入{{mediaType === 'audio' ? '音频' : '视频'}}描述（可选）" value="{{description}}" maxlength="200" bindinput="onDescriptionInput" auto-height />
      </view>
    </view>


  </view>

  <!-- Footer Actions -->
  <view class="footer">
    <!-- Initial State -->
    <block wx:if="{{!uploading}}">
      <button class="primary-button" bindtap="uploadMedia" disabled="{{!selectedFile || !title || title.trim().length === 0}}">
        开始上传
      </button>
    </block>
    <!-- Uploading State -->
    <block wx:else>
      <button class="primary-button loading" disabled>
        <van-loading color="#FFFFFF" size="20px" />
        <text>上传中...</text>
      </button>
    </block>
  </view>
</view>