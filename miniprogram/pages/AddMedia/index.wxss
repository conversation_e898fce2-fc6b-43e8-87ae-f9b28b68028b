/* pages/AddMedia/index.wxss */

/* Global styles */
page {
  background-color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  height: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding-bottom: 140rpx; /* Space for the footer */
  box-sizing: border-box;
}

/* Header styles */
.header {
  background-color: #ff7f11;
  padding: 40rpx 30rpx;
  color: #fff;
}

.header-title {
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 26rpx;
  opacity: 0.9;
}

/* Main content */
.content {
  flex: 1;
  padding: 30rpx;
}

/* Section styling */
.section {
  background-color: #fff;
  border-radius: 0;
  padding: 30rpx;
  margin-bottom: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.required {
  color: #ff4d4f;
  margin-right: 6rpx;
}

.character-count {
  font-size: 24rpx;
  color: #999;
}

.character-count.near-limit {
  color: #ff7f11;
}

/* Upload area */
.upload-area {
  border: 2rpx dashed #d9d9d9;
  border-radius: 8rpx;
  background-color: #fafafa;
  transition: all 0.3s;
}

.upload-area:active {
  border-color: #ff7f11;
  background-color: rgba(255, 127, 17, 0.05);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
  text-align: center;
}

.upload-icon {
  margin-bottom: 24rpx;
}

.upload-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
}

.upload-hint {
  font-size: 24rpx;
  color: #999;
}

/* File preview */
.file-preview {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background-color: #fff;
  border: 2rpx solid #ff7f11;
  border-radius: 8rpx;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-icon {
  margin-right: 16rpx;
}

.file-details {
  flex: 1;
}

.file-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
  word-break: break-all;
}

.file-size {
  font-size: 24rpx;
  color: #999;
}

.file-actions {
  flex-shrink: 0;
}

.change-file-btn {
  font-size: 24rpx;
  padding: 8rpx 20rpx;
  background-color: transparent;
  color: #ff7f11;
  border: 1rpx solid #ff7f11;
  border-radius: 0;
  line-height: 1.5;
  margin: 0;
}

/* Input containers */
.input-container,
.textarea-container {
  border: 1rpx solid #f0f0f0;
  border-radius: 0;
  background-color: #fafafa;
  padding: 20rpx;
}

.title-input {
  width: 100%;
  font-size: 28rpx;
  color: #333;
  background-color: transparent;
  border: none;
  outline: none;
}

.description-textarea {
  width: 100%;
  min-height: 120rpx;
  font-size: 28rpx;
  color: #333;
  background-color: transparent;
  border: none;
  outline: none;
  line-height: 1.6;
}

/* Upload progress */
.upload-progress-section {
  background-color: #fafafa;
  border-top: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
}

.progress-container {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-bar {
  flex: 1;
  height: 12rpx;
  background-color: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
  margin-right: 16rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff7f11, #ff5500);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
  min-width: 60rpx;
  text-align: right;
}

.progress-status {
  font-size: 26rpx;
  color: #999;
}

/* Footer */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 20rpx 30rpx calc(20rpx + env(safe-area-inset-bottom));
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
}

.primary-button {
  flex: 1;
  height: 80rpx;
  border-radius: 0;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 10rpx;
  border: none;
  gap: 12rpx;
}

.primary-button {
  background-color: #ff7f11;
  color: #fff;
}

.primary-button[disabled] {
  background-color: #ffd0a8;
  color: #fff;
}

.primary-button.loading {
  background-color: #ff7f11;
  color: #fff;
}
