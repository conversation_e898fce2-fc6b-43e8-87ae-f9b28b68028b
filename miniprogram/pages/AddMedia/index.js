// pages/AddMedia/index.js
const app = getApp();
const { getUrl, PostData, GetData } = require("../../common/server");

Page({
  /**
   * 页面的初始数据
   */
  data: {
    mediaType: "audio", // 'audio' or 'video'
    selectedFile: null,
    title: "",
    description: "",
    uploading: false,
    uploadProgress: 0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 根据传入的 type 参数确定是上传音频还是视频
    const type = options.type || "audio";
    this.setData({
      mediaType: type,
    });

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: type === "audio" ? "上传音频" : "上传视频",
    });
  },

  /**
   * 选择媒体文件
   */
  chooseMedia() {
    const { mediaType } = this.data;

    if (mediaType === "audio") {
      // 选择音频文件
      wx.chooseMessageFile({
        count: 1,
        type: "file",
        extension: ["mp3", "wav", "m4a", "aac"],
        success: (res) => {
          console.log("res:", res);
          const file = res.tempFiles[0];
          this.setData({
            selectedFile: {
              path: file.path,
              name: file.name,
              size: this.formatFileSize(file.size),
            },
          });
        },
        fail: (err) => {
          console.error("选择音频文件失败:", err);
          wx.showToast({
            title: "选择文件失败",
            icon: "none",
          });
        },
      });
    } else {
      // 选择视频文件
      wx.chooseVideo({
        count: 1,
        sourceType: ["album"],
        // maxDuration: 300, // 最大5分钟
        // camera: "back",
        success: (res) => {
          console.log("选择视频文件:", res);
          const tempFilePath = res.tempFilePath;
          // this.setData({ videoUrl: tempFilePath });
          // this.uploadVideo(tempFilePath);

          this.setData({
            selectedFile: {
              path: tempFilePath,
              name: `video_${Date.now()}.mp4`,
              size: this.formatFileSize(res.size),
            },
          });
        },
        fail: (err) => {
          console.error("选择视频文件失败:", err);
          wx.showToast({
            title: "选择视频失败",
            icon: "none",
          });
        },
      });
    }
  },
  /**
   * 格式化文件大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  },

  /**
   * 标题输入
   */
  onTitleInput(e) {
    this.setData({
      title: e.detail.value,
    });
  },

  /**
   * 描述输入
   */
  onDescriptionInput(e) {
    this.setData({
      description: e.detail.value,
    });
  },

  /**
   * 上传媒体文件
   */
  uploadMedia() {
    const { selectedFile, title, description, mediaType } = this.data;

    if (!selectedFile) {
      wx.showToast({
        title: "请选择文件",
        icon: "none",
      });
      return;
    }

    if (!title || title.trim().length === 0) {
      wx.showToast({
        title: "请输入标题",
        icon: "none",
      });
      return;
    }

    this.setData({
      uploading: true,
      uploadProgress: 0,
    });

    // 确定上传接口
    const uploadUrl =
      mediaType === "audio" ? getUrl("uploadAudio") : getUrl("uploadVideo");

    // 创建上传任务
    const uploadTask = wx.uploadFile({
      url: uploadUrl,
      filePath: selectedFile.path,
      name: "file",
      formData: {
        open_id: "admin",
        title: title.trim(),
        description: description.trim(),
      },
      header: {
        Authorization: app.globalData.token || "",
      },
      success: (res) => {
        console.log("上传成功:", res);
        try {
          const data = JSON.parse(res.data);
          if (data.code === 200 || data.success) {
            wx.showToast({
              title: "上传成功",
              icon: "success",
            });

            // 延迟返回上一页
            setTimeout(() => {
              wx.navigateBack();
            }, 1500);
          } else {
            wx.showToast({
              title: `${data.code || ""}:${data?.message ?? ""}`,
              icon: "none",
              mask: true,
              duration: 2300,
            });
          }
        } catch (e) {
          console.error("解析响应失败:", e);
          wx.showToast({
            title: "上传失败",
            icon: "none",
          });
        }
      },
      fail: (err) => {
        console.error("上传失败:", err);
        wx.showToast({
          title: "上传失败，请重试",
          icon: "none",
        });
      },
      complete: () => {
        this.setData({
          uploading: false,
          uploadProgress: 0,
        });
      },
    });

    // 监听上传进度
    uploadTask.onProgressUpdate((res) => {
      this.setData({
        uploadProgress: res.progress,
      });
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: `上传${this.data.mediaType === "audio" ? "音频" : "视频"}模型`,
      path: `/pages/AddMedia/index?type=${this.data.mediaType}`,
    };
  },
});
