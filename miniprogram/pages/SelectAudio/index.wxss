/* pages/SelectAudio/index.wxss */
.container {
  display: flex;
  flex-direction: column;
  padding: 30rpx;
  background-color: #f8f8f8;
  height: 100vh;
  box-sizing: border-box;
}

.search-box {
  margin-bottom: 30rpx;
}

.audio-list {
  margin-bottom: 40rpx;
  flex-shrink: 1;
  flex-grow: 1;
  overflow-y: auto;
}

.audio-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.audio-item.selected {
  border: 2rpx solid #ff7f11;
  background-color: rgba(255, 127, 17, 0.05);
}

.audio-info {
  flex: 1;
}

.audio-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.audio-duration {
  font-size: 24rpx;
  color: #999;
}

.audio-controls {
  display: flex;
  align-items: center;
}

.play-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ff7f11;
}

.upload-section {
  margin-bottom: 40rpx;
}

.upload-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f2f2f2;
  color: #666;
  font-size: 28rpx;
  padding: 20rpx;
  border-radius: 12rpx;
  border: 1rpx dashed #ddd;
}

.upload-btn text {
  margin-left: 10rpx;
}

.bottom-actions {
  display: flex;
  justify-content: space-between;
}

.cancel-btn,
.confirm-btn {
  width: 48%;
  padding: 20rpx 0;
  border-radius: 12rpx;
  font-size: 32rpx;
}

.cancel-btn {
  background-color: #f2f2f2;
  color: #666;
}

.confirm-btn {
  background-color: #ff7f11;
  color: #fff;
}

.confirm-btn[disabled] {
  background-color: #ffd0a6;
  color: #fff;
}

/* Loading State */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
  flex: 1;
}

/* Error State */
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
  flex: 1;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.error-message {
  font-size: 28rpx;
  color: #666;
  margin: 20rpx 0 30rpx;
  line-height: 1.5;
}

.retry-btn {
  background-color: #ff7f11;
  color: #fff;
  font-size: 28rpx;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  border: none;
  transition: all 0.3s ease;
}

.retry-btn:active {
  background-color: #e6700f;
  transform: scale(0.98);
}

/* Empty State */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
  flex: 1;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.empty-message {
  font-size: 28rpx;
  color: #999;
  margin: 20rpx 0 30rpx;
}

/* Audio Description */
.audio-description {
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
