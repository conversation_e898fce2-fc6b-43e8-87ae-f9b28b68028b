// pages/SelectAudio/index.js
const app = getApp();

const { PostData } = require("../../common/server");
const { formatDuration } = require("../../utils/utils");

Page({
  /**
   * 页面的初始数据
   */
  data: {
    audioList: [
      {
        id: "1",
        title: "男声音频模型1",
        duration: "02:35",
        url: "https://example.com/audio1.mp3",
        type: "male",
      },
      {
        id: "2",
        title: "女声音频模型1",
        duration: "03:12",
        url: "https://example.com/audio2.mp3",
        type: "female",
      },
      {
        id: "3",
        title: "男声音频模型2",
        duration: "01:48",
        url: "https://example.com/audio3.mp3",
        type: "male",
      },
      {
        id: "4",
        title: "女声音频模型2",
        duration: "04:05",
        url: "https://example.com/audio4.mp3",
        type: "female",
      },
      {
        id: "5",
        title: "儿童音频模型",
        duration: "02:10",
        url: "https://example.com/audio5.mp3",
        type: "child",
      },
      {
        id: "6",
        title: "儿童音频模型",
        duration: "02:10",
        url: "https://example.com/audio5.mp3",
        type: "child",
      },
    ],
    filteredAudioList: [],
    searchValue: "",
    selectedAudioId: "",
    playingAudioId: "",
    audioContext: null,
    // 加载状态
    isLoading: false,
    isError: false,
    errorMessage: "",
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 创建音频上下文
    this.audioContext = wx.createInnerAudioContext();
    this.audioContext.onEnded(() => {
      this.setData({
        playingAudioId: "",
      });
    });

    // 获取音频列表数据
    this.getAudioList();

    // 获取页面事件通道
    try {
    } catch (e) {}
    // const eventChannel = this.getOpenerEventChannel();

    // 监听上一个页面传递的数据
    // eventChannel.on("selectedAudioData", (data) => {
    //   if (data && data.audio) {
    //     // 设置已选中的音频
    //     const selectedAudio = data.audio;
    //     const selectedId = selectedAudio.id;

    //     this.setData({
    //       selectedAudioId: selectedId,
    //     });
    //   }
    // });
  },

  /**
   * 获取音频列表
   */
  getAudioList() {
    const _this = this;

    // 设置加载状态
    this.setData({
      isLoading: true,
      isError: false,
      errorMessage: "",
    });

    PostData({
      url: "getAudioLst",
      params: {
        open_id: "admin",
        page: 1,
        pageSize: 10000,
      },
    })
      .then((res) => {
        console.log("获取音频列表成功:", res);

        if (res.code === 200) {
          const list =
            res.data?.list?.map((item) => {
              return {
                id: item._id,
                uuid: item.uuid,
                title: item.title,
                duration: formatDuration(item.duration),
                url: item.oss_url,
                audioUrl: item.oss_url,
                type: item.type || "custom",
                description: item.description,
                createTime: item.create_at,
                coverUrl: item.cover,
              };
            }) || [];

          _this.setData({
            audioList: list,
            filteredAudioList: list,
            isLoading: false,
          });
        } else {
          throw new Error(res.message || "获取音频列表失败");
        }
      })
      .catch((error) => {
        console.error("获取音频列表失败:", error);

        _this.setData({
          isLoading: false,
          isError: true,
          errorMessage: error.message || "网络错误，请稍后重试",
        });

        // 显示错误提示
        wx.showToast({
          title: error.message || "获取音频列表失败",
          icon: "none",
          duration: 2000,
        });

        // 如果获取失败，使用默认数据
        _this.setData({
          filteredAudioList: _this.data.audioList,
        });
      });
  },

  /**
   * 重新加载音频列表
   */
  reloadAudioList() {
    this.getAudioList();
  },

  /**
   * 搜索音频
   */
  onSearchChange(e) {
    const searchValue = e.detail;
    this.setData({
      searchValue,
    });

    if (!searchValue) {
      this.setData({
        filteredAudioList: this.data.audioList,
      });
      return;
    }

    const filteredList = this.data.audioList.filter((item) =>
      item.title.includes(searchValue)
    );

    this.setData({
      filteredAudioList: filteredList,
    });
  },

  /**
   * 选择音频
   */
  selectAudio(e) {
    const id = e.currentTarget.dataset.id;
    this.setData({
      selectedAudioId: id,
    });
  },

  /**
   * 播放/暂停音频
   */
  playAudio(e) {
    const id = e.currentTarget.dataset.id;
    const { playingAudioId } = this.data;

    // 如果点击的是当前正在播放的音频，则暂停
    if (playingAudioId === id) {
      this.audioContext.pause();
      this.setData({
        playingAudioId: "",
      });
      return;
    }

    // 如果有其他音频在播放，先停止
    if (playingAudioId) {
      this.audioContext.stop();
    }

    // 播放选中的音频
    const selectedAudio = this.data.audioList.find((item) => item.id === id);
    this.audioContext.src = selectedAudio.url;
    this.audioContext.play();

    this.setData({
      playingAudioId: id,
    });
  },

  /**
   * 上传新音频
   */
  // uploadAudio() {
  //   wx.chooseMessageFile({
  //     count: 1,
  //     type: "file",
  //     extension: ["mp3", "wav", "aac"],
  //     success: (res) => {
  //       const tempFilePath = res.tempFiles[0].path;
  //       const fileName = res.tempFiles[0].name;

  //       // 这里可以添加上传到服务器的逻辑
  //       wx.showLoading({
  //         title: "上传中...",
  //       });

  //       // 模拟上传过程
  //       setTimeout(() => {
  //         wx.hideLoading();

  //         // 添加到列表中
  //         const newAudio = {
  //           id: String(this.data.audioList.length + 1),
  //           name: fileName,
  //           duration: "00:00", // 实际应用中应该获取真实时长
  //           url: tempFilePath,
  //           type: "custom",
  //         };

  //         const updatedList = [...this.data.audioList, newAudio];

  //         this.setData({
  //           audioList: updatedList,
  //           filteredAudioList: updatedList,
  //           selectedAudioId: newAudio.id,
  //         });

  //         wx.showToast({
  //           title: "上传成功",
  //           icon: "success",
  //         });
  //       }, 1500);
  //     },
  //   });
  // },

  /**
   * 取消选择
   */
  onCancel() {
    // 停止正在播放的音频
    if (this.data.playingAudioId) {
      this.audioContext.stop();
    }

    // 返回上一页
    wx.navigateBack();
  },

  /**
   * 确认选择
   */
  onConfirm() {
    if (!this.data.selectedAudioId) {
      wx.showToast({
        title: "请选择一个音频",
        icon: "none",
      });
      return;
    }

    // 停止正在播放的音频
    if (this.data.playingAudioId) {
      this.audioContext.stop();
    }

    const selectedAudio = this.data.audioList.find(
      (item) => item.id === this.data.selectedAudioId
    );

    // 获取页面事件通道
    const eventChannel = this.getOpenerEventChannel();

    // 将选中的音频数据传回上一页
    eventChannel.emit("selectAudioCallback", selectedAudio);

    wx.navigateBack();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 销毁音频上下文
    if (this.audioContext) {
      this.audioContext.destroy();
    }
  },
});
