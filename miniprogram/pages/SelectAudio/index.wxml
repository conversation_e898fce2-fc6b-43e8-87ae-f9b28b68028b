<!--pages/SelectAudio/index.wxml-->
<view class="container">

  <view class="search-box">
    <van-search value="{{ searchValue }}" placeholder="搜索音频" bind:change="onSearchChange" shape="round" />
  </view>

  <!-- Loading State -->
  <view class="loading-container" wx:if="{{ isLoading }}">
    <van-loading type="spinner" size="24px" color="#ff7f11">加载中...</van-loading>
  </view>

  <!-- Error State -->
  <view class="error-container" wx:elif="{{ isError }}">
    <view class="error-content">
      <van-icon name="warning-o" size="48rpx" color="#ff4d4f" />
      <view class="error-message">{{ errorMessage }}</view>
      <button class="retry-btn" bindtap="reloadAudioList">重新加载</button>
    </view>
  </view>

  <!-- Audio List -->
  <view class="audio-list" wx:else>
    <!-- Empty State -->
    <view class="empty-container" wx:if="{{ filteredAudioList.length === 0 }}">
      <view class="empty-content">
        <van-icon name="music-o" size="64rpx" color="#ccc" />
        <view class="empty-message">暂无音频数据</view>
        <button class="retry-btn" bindtap="reloadAudioList">重新加载</button>
      </view>
    </view>

    <!-- Audio Items -->
    <block wx:else>
      <block wx:for="{{ filteredAudioList }}" wx:key="id">
        <view class="audio-item {{ selectedAudioId === item.id ? 'selected' : '' }}" bindtap="selectAudio" data-id="{{ item.id }}">
          <view class="audio-info">
            <view class="audio-name">{{ item.title }}</view>
            <view class="audio-duration">时长: {{ item.duration }}</view>
            <view class="audio-description" wx:if="{{ item.description }}">{{ item.description }}</view>
          </view>
          <view class="audio-controls">
            <view class="play-btn" catchtap="playAudio" data-id="{{ item.id }}">
              <van-icon name="{{ playingAudioId === item.id ? 'pause-circle-o' : 'play-circle-o' }}" size="48rpx" />
            </view>
          </view>
        </view>
      </block>
    </block>
  </view>

  <!-- <view class="upload-section">
    <button class="upload-btn" bindtap="uploadAudio">
      <van-icon name="plus" size="24rpx" />
      <text>上传新音频</text>
    </button>
  </view> -->

  <view class="bottom-actions">
    <button class="cancel-btn" bindtap="onCancel">取消</button>
    <button class="confirm-btn" bindtap="onConfirm" disabled="{{ !selectedAudioId }}">确认选择</button>
  </view>
</view>