const { PostData } = require("../../common/server");

// pages/Home/index.js
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    greeting: "你好，",
    userName: "",
    userAvatar: "",
    dayPeriod: "工作日",
    currentDate: "",

    // 创作统计
    stats: {
      contentCount: 0,
      mediaCount: 0,
      totalCount: 0,
    },

    // 最近活动
    recentActivities: [
      {
        id: "1",
        type: "content",
        icon: "edit",
        title: "创作了一篇文章",
        description: "《AI助手使用指南》",
        date: "今天",
        time: "14:30",
      },
      {
        id: "2",
        type: "media",
        icon: "music-o",
        title: "合成了一段音频",
        description: "专业女声解说",
        date: "昨天",
        time: "09:15",
      },
      {
        id: "3",
        type: "content",
        icon: "filter-o",
        title: "洗稿完成",
        description: "产品介绍文案优化",
        date: "3天前",
        time: "18:45",
      },
    ],
  },
  tabUrl(e) {
    const url = e.currentTarget.dataset.url;
    wx.switchTab({
      url: url,
    });
  },
  /**
   * 页面跳转
   */
  gotoUrl(e) {
    const url = e.currentTarget.dataset.url;
    wx.navigateTo({
      url: url,
    });
  },

  /**
   * 点击头像
   */
  onTapAvatar() {
    wx.navigateTo({
      url: "/pages/My/index",
    });
  },

  /**
   * 点击活动项
   */
  onTapActivity(e) {
    const id = e.currentTarget.dataset.id;
    const type = e.currentTarget.dataset.type;

    // 根据活动类型和ID跳转到相应页面
    console.log("点击了活动:", id, type);

    // 这里可以根据实际需求跳转到不同页面
    if (type === "content") {
      wx.navigateTo({
        url: `/pages/ContentDetail/index?id=${id}`,
      });
    } else if (type === "media") {
      wx.navigateTo({
        url: `/pages/AudioDemo/index?id=${id}`,
      });
    }
  },

  /**
   * 查看所有内容创作工具
   */
  // showAllContentTools() {
  //   wx.showToast({
  //     title: "查看全部内容创作工具",
  //     icon: "none",
  //   });
  // },

  /**
   * 查看所有媒体工具
   */
  // showAllMediaTools() {
  //   wx.showToast({
  //     title: "查看全部媒体工具",
  //     icon: "none",
  //   });
  // },

  /**
   * 查看所有设置
   */
  // showAllSettings() {
  //   wx.showToast({
  //     title: "查看全部设置",
  //     icon: "none",
  //   });
  // },

  /**
   * 查看所有活动
   */
  // viewAllActivities() {
  //   wx.showToast({
  //     title: "查看全部活动",
  //     icon: "none",
  //   });
  // },

  /**
   * 获取用户信息
   */
  getUserInfo() {
    // 尝试从缓存获取用户信息
    const userInfo = wx.getStorageSync("userInfo") || {
      openId: "admin",
    };
    if (userInfo) {
      PostData({
        url: "userInformation",
        params: {
          open_id: userInfo.openId,
          process: 6,
        },
      }).then((res) => {
        console.log("用户信息:", res);
        this.setData({
          userName: res.nickName || "创作者",
          userAvatar: res.avatarUrl,
          stats: {
            contentCount: res.data.articleCount.count,
            mediaCount: res.data.videoCount.count,
            totalCount: res.data.articleCount.count + res.data.videoCount.count,
          },
        });
        // 默认人设缓存到本地
        wx.setStorageSync("selectIpSetting", {
          default: res.data.defaultIpSetting,
        });
        // 个人信息设置到本地
        wx.setStorageSync("userInfo", res.data.userDetail);
      });
    }

    // 设置问候语和日期
    this.setGreeting();
    this.setCurrentDate();
    this.setDayPeriod();
  },

  /**
   * 设置问候语
   */
  setGreeting() {
    const hour = new Date().getHours();
    let greeting = "你好，";

    if (hour < 6) {
      greeting = "凌晨好，";
    } else if (hour < 9) {
      greeting = "早上好，";
    } else if (hour < 12) {
      greeting = "上午好，";
    } else if (hour < 14) {
      greeting = "中午好，";
    } else if (hour < 17) {
      greeting = "下午好，";
    } else if (hour < 19) {
      greeting = "傍晚好，";
    } else {
      greeting = "晚上好，";
    }

    this.setData({
      greeting,
    });
  },

  /**
   * 设置当前日期
   */
  setCurrentDate() {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    const day = now.getDate();

    const formattedDate = `${year}.${month < 10 ? "0" + month : month}.${
      day < 10 ? "0" + day : day
    }`;
    this.setData({
      currentDate: formattedDate,
    });
  },

  /**
   * 设置日期类型（工作日/周末）
   */
  setDayPeriod() {
    const now = new Date();
    const day = now.getDay(); // 0 是周日，6 是周六

    let dayPeriod = "工作日";
    if (day === 0 || day === 6) {
      dayPeriod = "周末";
    }

    this.setData({
      dayPeriod,
    });
  },

  /**
   * 获取用户的openId
   */
  getOpenInformation() {
    wx.cloud
      .callFunction({
        name: "bgm",
        data: {},
      })
      .then((res) => {
        wx.setStorageSync("openid", res.result.openid);
      })
      .catch((err) => {
        console.error("获取openid失败:", err);
      });
  },

  /**
   * 获取创作统计
   */
  getCreationStats() {
    // 这里可以从服务器获取创作统计数据
    // 目前使用的是模拟数据
    console.log("获取创作统计");

    // 模拟数据
    const stats = {
      contentCount: Math.floor(Math.random() * 20) + 5,
      mediaCount: Math.floor(Math.random() * 15) + 3,
      get totalCount() {
        return this.contentCount + this.mediaCount;
      },
    };

    this.setData({
      stats,
    });
  },

  /**
   * 获取最近活动
   */
  getRecentActivities() {
    // 这里可以从服务器获取最近活动数据
    // 目前使用的是模拟数据
    console.log("获取最近活动");
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getOpenInformation();
    this.getUserInfo();
    // this.getCreationStats();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时更新问候语和日期
    this.setGreeting();
    this.setCurrentDate();

    // 获取最新数据
    // this.getCreationStats();
    this.getRecentActivities();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 刷新数据
    this.getUserInfo();
    this.getCreationStats();
    this.getRecentActivities();

    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },
});
