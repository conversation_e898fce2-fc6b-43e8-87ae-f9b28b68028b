<!-- 高级背景效果 -->
<view class="premium-background">
  <view class="gradient-overlay"></view>
  <view class="particle-container">
    <view wx:for="{{20}}" wx:key="index" class="particle particle-{{index % 5}}"></view>
  </view>
  <view class="noise-texture"></view>
</view>

<view class="container">
  <!-- 顶部安全区域 -->
  <view class="safe-area-top"></view>

  <!-- 顶部状态栏 -->
  <view class="status-bar">
    <view class="status-item">
      <van-icon name="clock-o" size="28rpx" />
      <text class="status-text">{{ currentDate }}</text>
    </view>
    <view class="premium-badge">
      <van-icon name="diamond-o" size="24rpx" />
      <text>专业版</text>
    </view>
  </view>

  <!-- 顶部欢迎区域 -->
  <view class="header">
    <view class="welcome-container">
      <view class="greeting-line">
        <view class="greeting">{{ greeting }}</view>
        <view class="day-indicator">{{ dayPeriod }}</view>
      </view>
      <view class="user-name">{{ userName || '创作者' }}</view>
      <view class="user-tagline">让AI为您的创作助力</view>
    </view>
    <view class="avatar-wrapper" bindtap="onTapAvatar">
      <view class="avatar-container">
        <image class="avatar" src="{{ userAvatar || '/assets/default-avatar.png' }}" mode="aspectFill"></image>
      </view>
      <view class="avatar-status"></view>
    </view>
  </view>

  <!-- 创作统计卡片 -->
  <view class="stats-card">
    <view class="stats-item">
      <view class="stats-value">{{ stats.contentCount || 0 }}</view>
      <view class="stats-label">内容创作</view>
    </view>
    <view class="stats-divider"></view>
    <view class="stats-item">
      <view class="stats-value">{{ stats.mediaCount || 0 }}</view>
      <view class="stats-label">音视频</view>
    </view>
    <view class="stats-divider"></view>
    <view class="stats-item">
      <view class="stats-value">{{ stats.totalCount || 0 }}</view>
      <view class="stats-label">总创作</view>
    </view>
  </view>

  <!-- 主要创作按钮 -->
  <view class="create-button" data-url="/pages/CreateTitle/index" bind:tap="gotoUrl">
    <view class="create-button-glow"></view>
    <view class="create-button-content">
      <view class="create-button-text">
        <view class="create-button-title">开始创作</view>
        <view class="create-button-subtitle">快速生成高质量内容</view>
      </view>
      <view class="create-button-icon">
        <van-icon name="plus" size="24px" color="#fff" />
      </view>
    </view>
  </view>

  <!-- 功能分类区域 -->
  <view class="section-container">
    <view class="section-header">
      <view class="section-title-container">
        <view class="section-title-icon content">
          <van-icon name="edit" />
        </view>
        <view class="section-title">内容创作</view>
      </view>
      <!-- <view class="section-more" bindtap="showAllContentTools">
        <text>全部</text>
        <van-icon name="arrow" />
      </view> -->
    </view>

    <view class="function-grid">
      <view class="function-item" data-url="/pages/ReserveVideo/index" bind:tap="gotoUrl">
        <view class="function-icon-wrapper content">
          <view class="function-icon-bg"></view>
          <view class="function-icon">
            <van-icon name="logistics" />
          </view>
        </view>
        <view class="function-name">批量创作</view>
      </view>
      <view class="function-item" data-url="/pages/DraftWashing/index" bind:tap="gotoUrl">
        <view class="function-icon-wrapper content">
          <view class="function-icon-bg"></view>
          <view class="function-icon">
            <van-icon name="filter-o" />
          </view>
        </view>
        <view class="function-name">我要洗稿</view>
      </view>
      <view class="function-item" data-url="/pages/Inspiration/index" bind:tap="gotoUrl">
        <view class="function-icon-wrapper content">
          <view class="function-icon-bg"></view>
          <view class="function-icon">
            <van-icon name="bulb-o" />
          </view>
        </view>
        <view class="function-name">AI 灵感</view>
      </view>
      <view class="function-item" data-url="/pages/HotDot/index" bind:tap="gotoUrl">
        <view class="function-icon-wrapper content">
          <view class="function-icon-bg"></view>
          <view class="function-icon">
            <van-icon name="fire-o" />
          </view>
        </view>
        <view class="function-name">最新热点</view>
      </view>
    </view>
  </view>

  <view class="section-container">
    <view class="section-header">
      <view class="section-title-container">
        <view class="section-title-icon media">
          <van-icon name="music" />
        </view>
        <view class="section-title">音视频工具</view>
      </view>
      <!-- <view class="section-more" bindtap="showAllMediaTools">
        <text>全部</text>
        <van-icon name="arrow" />
      </view> -->
    </view>

    <view class="function-grid">
      <view class="function-item" data-url="/pages/CreateAudio/index" bind:tap="gotoUrl">
        <view class="function-icon-wrapper media">
          <view class="function-icon-bg"></view>
          <view class="function-icon">
            <van-icon name="music-o" />
          </view>
        </view>
        <view class="function-name">合成音频</view>
      </view>
      <view class="function-item" data-url="/pages/CreateAudioVideo/index" bind:tap="gotoUrl">
        <view class="function-icon-wrapper media">
          <view class="function-icon-bg"></view>
          <view class="function-icon">
            <van-icon name="video-o" />
          </view>
        </view>
        <view class="function-name">音视频合成</view>
      </view>
      <view class="function-item" data-url="/pages/CopyAudio/index" bind:tap="gotoUrl">
        <view class="function-icon-wrapper media">
          <view class="function-icon-bg"></view>
          <view class="function-icon">
            <van-icon name="volume-o" />
          </view>
        </view>
        <view class="function-name">音频采集</view>
      </view>
      <!-- <view class="function-item" data-url="/pages/SelectAudio/index" bind:tap="gotoUrl">
        <view class="function-icon-wrapper media">
          <view class="function-icon-bg"></view>
          <view class="function-icon">
            <van-icon name="play-circle-o" />
          </view>
        </view>
        <view class="function-name">音频模型</view>
      </view> -->
    </view>
  </view>

  <view class="section-container">
    <view class="section-header">
      <view class="section-title-container">
        <view class="section-title-icon settings">
          <van-icon name="setting" />
        </view>
        <view class="section-title">个性化设置</view>
      </view>
      <!-- <view class="section-more" bindtap="showAllSettings">
        <text>全部</text>
        <van-icon name="arrow" />
      </view> -->
    </view>

    <view class="function-grid">
      <view class="function-item" data-url="/pages/IPList/index" bind:tap="gotoUrl">
        <view class="function-icon-wrapper settings">
          <view class="function-icon-bg"></view>
          <view class="function-icon">
            <van-icon name="flag-o" />
          </view>
        </view>
        <view class="function-name">IP人设</view>
      </view>
      <view class="function-item" data-url="/pages/Workflow/index" bind:tap="gotoUrl">
        <view class="function-icon-wrapper settings">
          <view class="function-icon-bg"></view>
          <view class="function-icon">
            <van-icon name="cluster-o" />
          </view>
        </view>
        <view class="function-name">工作流</view>
      </view>
      <view class="function-item" data-url="/pages/PrivateModel/index" bind:tap="tabUrl">
        <view class="function-icon-wrapper settings">
          <view class="function-icon-bg"></view>
          <view class="function-icon">
            <van-icon name="lock" />
          </view>
        </view>
        <view class="function-name">我的模型</view>
      </view>
      <!-- <view class="function-item" data-url="/pages/My/index" bind:tap="gotoUrl">
        <view class="function-icon-wrapper settings">
          <view class="function-icon-bg"></view>
          <view class="function-icon">
            <van-icon name="setting-o" />
          </view>
        </view>
        <view class="function-name">设置</view>
      </view> -->
    </view>
  </view>

  <!-- 最近活动 -->
  <view class="section-container" wx:if="{{ recentActivities.length > 0 }}">
    <view class="section-header">
      <view class="section-title-container">
        <view class="section-title-icon activity">
          <van-icon name="clock" />
        </view>
        <view class="section-title">最近活动</view>
      </view>
      <!-- <view class="section-more" bindtap="viewAllActivities">
        <text>查看全部</text>
        <van-icon name="arrow" />
      </view> -->
    </view>

    <view class="recent-activities">
      <view class="activity-item" wx:for="{{ recentActivities }}" wx:key="id" data-id="{{ item.id }}" data-type="{{ item.type }}" bindtap="onTapActivity">
        <view class="activity-time-column">
          <view class="activity-date">{{ item.date }}</view>
          <view class="activity-time">{{ item.time }}</view>
        </view>
        <view class="activity-content">
          <view class="activity-icon-wrapper {{ item.type }}">
            <view class="activity-icon">
              <van-icon name="{{ item.icon }}" />
            </view>
          </view>
          <view class="activity-info">
            <view class="activity-title">{{ item.title }}</view>
            <view class="activity-description">{{ item.description || '点击查看详情' }}</view>
          </view>
        </view>
        <view class="activity-arrow">
          <van-icon name="arrow" />
        </view>
      </view>
    </view>
  </view>
</view>