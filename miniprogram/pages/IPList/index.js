// pages/IPList/index.js
const app = getApp();
const {
  PostData
} = require("../../common/server")
Page({
  /**
   * 页面的初始数据
   */
  isLoaded: false,
  data: {
    type: "",
    manageState: false,
    isLoading: false,
    showDeleteDialog: false,
    deleteId: null,
    ipList: [
      // {
      //   id: "1",
      //   ip_name: "法律咨询师",
      //   describe: "专业的法律咨询师，擅长民法、刑法等领域",
      //   default: true,
      // },
      // {
      //   id: "2",
      //   ip_name: "心理咨询师",
      //   describe: "专业的心理咨询师，擅长情感、职场等领域",
      //   default: false,
      // },
      // {
      //   id: "3",
      //   ip_name: "教育顾问",
      //   describe: "专业的教育顾问，擅长学习规划、考试辅导等领域",
      //   default: false,
      // },
    ],
  },

  /**
   * 切换管理状态
   */
  manageEvent() {
    this.setData({
      manageState: !this.data.manageState,
    });
  },
  /**
   * 获取人设列表
   */
  getIPSetting() {
    const _this = this;
    this.setData({
      isLoading: true,
    });
    PostData({
      url: "ipSettingList",
      params: {
        open_id: "admin"
      }
    }).then((result) => {
      _this.setData({
        ipList: result.data.data.map((item) => {
          return {
            ...item,
            id: item._id
          }
        })
      })
      console.log("result:", result);
    }).catch((error) => {
      console.log("error:", error);
    }).finally(() => {
      this.setData({
        isLoading: false,
      });
    })
  },
  /**
   * 设置默认人设
   */
  setDefaultEvent(e) {
    const id = e.currentTarget.dataset.id;
    const list = JSON.parse(JSON.stringify(this.data.ipList)).map((item) => {
      return {
        ...item,
        default: id === item.id ? true : false,
      };
    });

    this.setData({
      ipList: list,
    });

    wx.showToast({
      title: "已设为默认",
      icon: "success",
      duration: 1500,
    });
  },

  /**
   * 显示删除确认对话框
   */
  showDeleteConfirm(e) {
    const id = e.currentTarget.dataset.id;
    this.setData({
      showDeleteDialog: true,
      deleteId: id,
    });
  },

  /**
   * 取消删除
   */
  cancelDelete() {
    this.setData({
      showDeleteDialog: false,
      deleteId: null,
    });
  },

  /**
   * 确认删除
   */
  confirmDelete() {
    const id = this.data.deleteId;
    if (!id) return;

    PostData({
      url: "ipSettingDelete",
      params: {
        id: id
      }
    }).then((res) => {
      console.log("res:", res);
      if (res.data.code === 200) {
        wx.showToast({
          title: "删除成功",
          icon: "success",
          duration: 1500,
          mask: true
        });
        this.isLoaded = true;
        this.getIPSetting();
      } else {
        wx.showToast({
          title: `${res.data.code??""}:${res.data?.message??""}`,
          icon: "none",
          mask: true
        })
      }

    }).catch((error) => {

    })
  },

  /**
   * 跳转到指定页面
   */
  gotoUrl(e) {
    // 如果是其他页面切换过来，选择选题的情况下，不进行跳转
    if (this.data.type) {
      return;
    }
    // 如果是在管理模式下，不进行跳转
    if (this.data.manageState) return;

    const url = e.currentTarget.dataset.url;
    const id = e.currentTarget.dataset.id;

    let navigateUrl = url;
    if (id) {
      navigateUrl += `?id=${id}`;
    }

    wx.navigateTo({
      url: navigateUrl,
    });
  },
  /**
   * 加载人设数据
   */
  loadIPData() {
    this.setData({
      isLoading: true,
    });

    // 模拟网络请求
    setTimeout(() => {
      this.setData({
        isLoading: false,
      });
    }, 1000);
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log("options", options);
    const {
      type
    } = options;
    console.log("type:", type)
    // 是否是其他页面切换过来
    if (this.data.type) {
      this.setData({
        type: type
      })
    } else {
      this.setData({
        type: ""
      })
    }
    this.isLoaded = true;
    this.getIPSetting();
  },
  onUnload() {
    this.isLoaded = false
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次页面显示时刷新数据
    if (this.isLoaded) {
      this.getIPSetting();
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadIPData();

    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },
});