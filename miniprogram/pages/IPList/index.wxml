<view class="container">
  <view class="header">
    <view class="header-title">人设列表</view>
    <view class="header-actions">
      <view class="action-btn {{ manageState ? 'active' : '' }}" bind:tap="manageEvent">
        <van-icon name="setting-o" size="32rpx" />
        <text>{{ manageState ? '完成' : '管理' }}</text>
      </view>
      <view class="action-btn add-btn" bind:tap="gotoUrl" data-url="/pages/IPDetail/index" data-id="">
        <van-icon name="plus" size="32rpx" />
        <text>新增人设</text>
      </view>
    </view>
  </view>

  <!-- Empty state -->
  <view class="empty-state" wx:if="{{ipList.length === 0}}">
    <image class="empty-image" src="/assets/empty-state.png" mode="aspectFit"></image>
    <view class="empty-text">暂无人设数据</view>
    <view class="empty-action" bind:tap="gotoUrl" data-url="/pages/IPDetail/index" data-id="">
      <text>添加第一个人设</text>
      <van-icon name="arrow" />
    </view>
  </view>

  <!-- IP List -->
  <view class="ip-list" wx:else>
    <view class="ip-item-wrapper" wx:for="{{ipList}}" wx:key="id" animation="{{item.animation}}">
      <view class="ip-item {{ manageState ? 'managing' : '' }} {{ item.default ? 'default' : '' }}" bind:tap="gotoUrl" data-url="/pages/IPDetail/index" data-id="{{item.uuid}}">
        <!-- Default badge -->
        <view class="default-badge" wx:if="{{item.default}}">
          <van-icon name="star" color="#FF7F11" size="24rpx" />
          <text>默认</text>
        </view>

        <view class="ip-content">
          <view class="ip-title">{{item.params.ip_name}}</view>
          <view class="ip-description">{{item.params.career_experience}}</view>
        </view>

        <view class="ip-action">
          <van-icon name="edit" size="40rpx" color="#999" />
        </view>
      </view>

      <!-- Management options -->
      <view class="ip-management" wx:if="{{manageState}}" style="height: {{manageState ? '100rpx' : '0'}}">
        <view class="management-option" catchtap="setDefaultEvent" data-id="{{item.uuid}}">
          <van-checkbox value="{{item.default}}" checked-color="#FF7F11" />
          <text>设为默认</text>
        </view>

        <view class="management-option delete" catchtap="showDeleteConfirm" data-id="{{item.uuid}}">
          <van-icon name="delete-o" color="#ff4d4f" size="32rpx" />
          <text>删除</text>
        </view>
      </view>
    </view>
  </view>

  <!-- Loading state -->
  <view class="loading-state" wx:if="{{isLoading}}">
    <van-loading color="#FF7F11" />
    <text>加载中...</text>
  </view>

  <!-- Delete confirmation dialog -->
  <van-dialog id="van-dialog" title="删除确认" message="确定要删除这个人设吗？此操作不可恢复。" show="{{ showDeleteDialog }}" show-cancel-button confirm-button-color="#FF7F11" bind:confirm="confirmDelete" bind:cancel="cancelDelete" />
</view>