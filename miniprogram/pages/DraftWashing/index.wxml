<view class="container">
  <!-- Header -->
  <view class="header">
    <view class="header-title">文本洗稿</view>
    <view class="header-subtitle">优化文本内容，提升表达质量</view>
  </view>

  <!-- Main Content -->
  <view class="content">
    <!-- Input Section -->
    <view class="section">
      <view class="section-header">
        <view class="section-title">
          <text class="required">*</text>
          <text>洗稿内容</text>
        </view>
        <view class="character-count {{write.length > 900 ? 'near-limit' : ''}}">
          {{write.length}}/1000
        </view>
      </view>
      <cs-text-area bind:valuechange="writeChangeHandler" maxValue="1000" height="320" placeholderTxt="请输入或粘贴要洗稿的内容" />
    </view>

    <!-- Requirements Section -->
    <view class="section">
      <view class="section-header">
        <view class="section-title">洗稿要求</view>
      </view>

      <view class="style-options">
        <view class="style-option-group">
          <view class="option-label">字数要求</view>
          <view class="style-buttons">
            <view class="style-btn {{writingNumber === 270 ? 'active' : '' }}" data-type="{{270}}" bindtap="selectStyleType">270字</view>
            <view class="style-btn {{writingNumber === 520 ? 'active' : '' }}" data-type="{{520}}" bindtap="selectStyleType">520字</view>
            <view class="style-btn {{writingNumber === 860 ? 'active' : '' }}" data-type="{{860}}" bindtap="selectStyleType">860字</view>
          </view>
        </view>

        <view class="style-option-group">
          <view class="option-label">结合人设</view>
          <van-switch checked="{{useIpSetting}}" active-color="#FF7F11" size="24px" bind:change="toggleIpSetting" />
        </view>
      </view>

      <!-- IP Setting Card (only shown when useIpSetting is true) -->
      <view class="ip-setting-card" wx:if="{{useIpSetting}}">
        <view class="ip-info">
          <view class="ip-title">{{ipSeting.params.ip_name}}</view>
          <view class="ip-description">{{ipSeting.params.career_experience}}</view>
        </view>
        <view class="ip-action">
          <button class="change-ip-btn" bindtap="changeIpSetting">更换</button>
        </view>
      </view>
    </view>

    <!-- Results Section (only shown after generation) -->
    <view class="section result-section" wx:if="{{showResult > 1}}">
      <view class="section-header">
        <view class="section-title">洗稿结果</view>
        <view class="result-actions" wx:if="{{showResult === 3}}">
          <view class="action-button small" bindtap="copyResult">
            <van-icon name="description" size="14px" />
            <text>复制</text>
          </view>
        </view>
      </view>

      <view class="result-container">
        <!-- Loading State -->
        <view class="result-loading" wx:if="{{afterWrite.length <= 0}}">
          <van-loading color="#FF7F11" size="36px" />
          <text class="loading-text">AI正在洗稿中...</text>
        </view>

        <!-- Result Content -->
        <view class="result-content" wx:else>
          <text class="result-text">{{afterWrite}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- Footer Actions -->
  <view class="footer">
    <!-- Initial State: Only Start Button -->
    <block wx:if="{{showResult === 1}}">
      <button class="primary-button" bindtap="updateWriteEvent" disabled="{{!write || write.trim().length === 0}}">
        开始洗稿
      </button>
    </block>

    <!-- Loading State -->
    <block wx:elif="{{showResult === 2}}">
      <button class="primary-button loading">
        <van-loading color="#FFFFFF" size="20px" />
        <text>洗稿中...</text>
      </button>
    </block>

    <!-- Result State: Regenerate and Create Buttons -->
    <block wx:elif="{{showResult === 3}}">
      <button class="secondary-button" data-type="again" bindtap="updateWriteEvent">
        重新生成
      </button>
      <button class="primary-button" bindtap="gotoUrl">
        开始创作
      </button>
    </block>
  </view>
</view>