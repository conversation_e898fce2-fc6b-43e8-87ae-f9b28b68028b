// pages/DraftWashing/index.js
const { wssData } = require("../../common/server");

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 文本内容
    write: "",
    afterWrite: "",

    // 状态控制
    showResult: 1, // 1、初始阶段 2、洗稿loading阶段 3、成功结果阶段 4、失败阶段
    socket: null,

    // 洗稿设置
    styleType: "polish", // 默认润色，可选值：polish(润色)、expand(扩写)、condense(缩写)
    useIpSetting: false, // 是否使用IP人设

    // IP人设信息
    ipSeting: {},
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 如果有传入的文本参数，则设置到文本框
    if (options.text) {
      const text = decodeURIComponent(options.text);
      this.setData({
        write: text,
      });
    }
  },

  /**
   * 跳转到创作页面
   */
  gotoUrl() {
    wx.redirectTo({
      url: "/pages/CreateIPVideo/index",
    });
  },

  /**
   * 清空输入内容
   */
  clearText() {
    this.setData({
      write: "",
    });

    wx.showToast({
      title: "已清空",
      icon: "success",
      duration: 1500,
    });
  },

  /**
   * 获取粘贴内容
   */
  GetClipboardData() {
    const scope = this;
    wx.getClipboardData({
      success(res) {
        if (res.data) {
          // 如果粘贴的内容超过1000字符，截取前1000个字符
          const text = res.data.slice(0, 1000);

          scope.setData({
            write: text,
          });

          wx.showToast({
            title: "已粘贴",
            icon: "success",
            duration: 1500,
          });
        }
      },
    });
  },

  /**
   * 输入内容更新
   */
  writeChangeHandler(e) {
    let value = e.detail.value;
    if (value.length > 1000) {
      value = value.substring(0, 1000);

      wx.showToast({
        title: "最多输入1000字",
        icon: "none",
        duration: 1500,
      });
    }

    this.setData({
      write: value,
    });
  },

  /**
   * 选择洗稿风格
   */
  selectStyleType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      styleType: type,
    });
  },

  /**
   * 切换是否使用IP人设
   */
  toggleIpSetting(e) {
    this.setData({
      useIpSetting: e.detail,
    });
  },

  /**
   * 更换IP人设
   */
  changeIpSetting() {
    wx.navigateTo({
      url: "/pages/IPListSelect/index",
    });
  },

  /**
   * 复制结果
   */
  copyResult() {
    const { afterWrite } = this.data;
    if (afterWrite) {
      wx.setClipboardData({
        data: afterWrite,
        success: () => {
          wx.showToast({
            title: "已复制到剪贴板",
            icon: "success",
            duration: 1500,
          });
        },
      });
    }
  },

  /**
   * 主动开始洗稿
   */
  updateWriteEvent() {
    let valuesData = "";
    let scope = this;

    // 验证输入内容
    if (!this.data.write || this.data.write.trim() === "") {
      wx.showToast({
        title: "请输入洗稿内容",
        icon: "none",
        duration: 1500,
      });
      return;
    }

    // 设置状态为加载中
    scope.setData({
      showResult: 2,
      afterWrite: "",
    });

    // 创建WebSocket连接
    const socket = wssData({
      url: "wssModify",
    });

    // 连接成功后发送请求
    socket.onOpen(() => {
      console.log("连接成功");
      scope.setData({
        socket: socket,
        showResult: 2,
      });

      // 构建请求数据
      const requestData = {
        type: "writingModify",
        content: scope.data.write,
        style: scope.data.styleType,
      };

      // 如果使用IP人设，添加人设信息
      if (scope.data.useIpSetting) {
        requestData.ipSetting = scope.data.ipSetting;
      }

      // 发送请求
      socket.send({
        data: JSON.stringify(requestData),
        success() {
          console.log("消息发送成功");
        },
        fail(err) {
          scope.setData({
            showResult: 4,
          });
          console.error("消息发送失败", err);

          wx.showToast({
            title: "请求失败，请重试",
            icon: "none",
            duration: 1500,
          });
        },
      });
    });

    // 接收消息
    socket.onMessage((res) => {
      console.log(res);
      const data = JSON.parse(res.data);

      if (data.status === "message") {
        valuesData += data.message;
        console.log(data.message);
        scope.setData({
          afterWrite: valuesData,
        });
      }

      if (data.status === "finish") {
        scope.setData({
          showResult: 3,
        });
        socket.close();

        wx.showToast({
          title: "洗稿完成",
          icon: "success",
          duration: 1500,
        });
      }
    });

    // 连接错误处理
    socket.onError((err) => {
      console.error("WebSocket连接错误", err);
      scope.setData({
        showResult: 4,
      });

      wx.showToast({
        title: "连接错误，请重试",
        icon: "none",
        duration: 1500,
      });
    });
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // 如果有正在进行的WebSocket连接，关闭它
    if (this.data.socket) {
      this.data.socket.close();
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 如果有正在进行的WebSocket连接，关闭它
    if (this.data.socket) {
      this.data.socket.close();
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: "文本洗稿工具 - AI助手",
      path: "/pages/DraftWashing/index",
      imageUrl: "/assets/share-image.png",
    };
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    const defaultIpSetting = wx.getStorageSync("selectIpSetting");
    if (defaultIpSetting && defaultIpSetting.checked) {
      const value = defaultIpSetting.checked;
      this.setData({
        ipSeting: {
          title: value.ip_name,
          subTitle: value.key_information,
          ...value,
        },
      });
      delete defaultIpSetting.checked;
      wx.setStorageSync("selectIpSetting", defaultIpSetting);
    } else if (defaultIpSetting && defaultIpSetting.default) {
      const value = defaultIpSetting.default;
      this.setData({
        ipSeting: {
          title: value.ip_name,
          subTitle: value.key_information,
          ...value,
        },
      });
    } else {
      this.setData({
        ipSeting: {
          title: "",
          subTitle: "",
        },
      });
    }
  },
});
