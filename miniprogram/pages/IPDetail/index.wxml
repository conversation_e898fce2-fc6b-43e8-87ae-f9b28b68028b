<view class="container">
  <!-- Form header -->
  <view class="form-header">
    <view class="header-title">{{isEdit ? '编辑IP人设' : '创建IP人设'}}</view>
    <view class="header-subtitle">完善IP信息，打造专属人设</view>
  </view>

  <!-- Form content -->
  <scroll-view scroll-y class="form-content" enhanced show-scrollbar="{{false}}">

    <!-- 1. 人设定位 -->
    <view class="section">
      <view class="section-title">
        <view class="section-number">1</view>
        <text>人设定位</text>
      </view>

      <!-- IP名称 -->
      <view class="form-item {{errors.ip_name ? 'has-error' : ''}}">
        <view class="form-label">
          <text class="required">*</text>
          IP名称
        </view>
        <view class="form-input">
          <input type="text" placeholder="请输入IP名称" value="{{ipSetting.ip_name}}" data-name="ip_name" bindinput="inputEvent" maxlength="20" />
        </view>
        <view class="error-message" wx:if="{{errors.ip_name}}">{{errors.ip_name}}</view>
      </view>

      <!-- 性别 -->
      <view class="form-item">
        <view class="form-label">
          <text class="required">*</text>
          性别
        </view>
        <view class="gender-selector">
          <view class="gender-option {{ipSetting.gender === '男' ? 'active' : ''}}" data-value="男" bindtap="genderSelectEvent">
            <view class="gender-icon">
              <van-icon name="manager" size="20px" color="{{ipSetting.gender === '男' ? '#fff' : '#ff7f11'}}" />
            </view>
            <text>男</text>
          </view>
          <view class="gender-option {{ipSetting.gender === '女' ? 'active' : ''}}" data-value="女" bindtap="genderSelectEvent">
            <view class="gender-icon">
              <van-icon name="friends" size="20px" color="{{ipSetting.gender === '女' ? '#fff' : '#ff7f11'}}" />
            </view>
            <text>女</text>
          </view>
        </view>
      </view>

      <!-- 年龄 -->
      <view class="form-item">
        <view class="form-label">
          <text class="required">*</text>
          年龄
        </view>
        <view class="form-input">
          <input type="number" placeholder="请输入年龄(18-99)" value="{{ipSetting.age}}" bindinput="ageInputEvent" maxlength="2" />
        </view>
      </view>

      <!-- 目前从事的工作 -->
      <view class="form-item {{errors.current_job ? 'has-error' : ''}}">
        <view class="form-label">
          <text class="required">*</text>
          目前在从事的是什么样的工作？
        </view>
        <view class="form-input">
          <input type="text" placeholder="请输入目前从事的工作" value="{{ipSetting.current_job}}" data-name="current_job" bindinput="inputEvent" maxlength="100" />
        </view>
        <view class="error-message" wx:if="{{errors.current_job}}">{{errors.current_job}}</view>
      </view>

      <!-- 职业经历 -->
      <view class="form-item">
        <view class="form-label">职业经历</view>
        <view class="form-textarea">
          <textarea placeholder="请输入职业经历" value="{{ipSetting.career_experience}}" data-name="career_experience" bindinput="inputEvent" maxlength="200" show-confirm-bar="{{false}}" />
        </view>
      </view>

      <!-- 人生经历 -->
      <view class="form-item">
        <view class="form-label">人生经历</view>
        <view class="form-textarea">
          <textarea placeholder="请输入人生经历" value="{{ipSetting.life_experience}}" data-name="life_experience" bindinput="inputEvent" maxlength="200" show-confirm-bar="{{false}}" />
        </view>
      </view>

      <!-- 兴趣爱好和特长 -->
      <view class="form-item">
        <view class="form-label">兴趣爱好和特长是什么？</view>
        <view class="form-textarea">
          <textarea placeholder="请输入兴趣爱好和特长" value="{{ipSetting.interests_skills}}" data-name="interests_skills" bindinput="inputEvent" maxlength="200" show-confirm-bar="{{false}}" />
        </view>
      </view>
    </view>

    <!-- 2. 商业定位 -->
    <view class="section">
      <view class="section-title">
        <view class="section-number">2</view>
        <text>商业定位</text>
      </view>

      <!-- 产品或服务 -->
      <view class="form-item {{errors.products_services ? 'has-error' : ''}}">
        <view class="form-label">
          <text class="required">*</text>
          您现在有什么产品，或者可以提供什么样服务？
        </view>
        <view class="form-textarea">
          <textarea placeholder="请输入您的产品或服务" value="{{ipSetting.products_services}}" data-name="products_services" bindinput="inputEvent" maxlength="300" show-confirm-bar="{{false}}" />
        </view>
        <view class="error-message" wx:if="{{errors.products_services}}">{{errors.products_services}}</view>
      </view>

      <!-- 竞争优势 -->
      <view class="form-item {{errors.competitive_advantages ? 'has-error' : ''}}">
        <view class="form-label">
          <text class="required">*</text>
          您的产品或服务，相较于同行，有哪些特色或者优势？
        </view>
        <view class="form-textarea">
          <textarea placeholder="请输入产品或服务的特色优势" value="{{ipSetting.competitive_advantages}}" data-name="competitive_advantages" bindinput="inputEvent" maxlength="300" show-confirm-bar="{{false}}" />
        </view>
        <view class="error-message" wx:if="{{errors.competitive_advantages}}">{{errors.competitive_advantages}}</view>
      </view>

      <!-- 变现方式 -->
      <view class="form-item {{errors.monetization_method ? 'has-error' : ''}}">
        <view class="form-label">
          <text class="required">*</text>
          您的短视频想通过什么方式变现？
        </view>
        <view class="form-textarea">
          <textarea placeholder="比如，直接挂车卖货，还是引流获客导流到私域等？" value="{{ipSetting.monetization_method}}" data-name="monetization_method" bindinput="inputEvent" maxlength="300" show-confirm-bar="{{false}}" />
        </view>
        <view class="error-message" wx:if="{{errors.monetization_method}}">{{errors.monetization_method}}</view>
      </view>
    </view>

    <!-- 3. 账号设定 -->
    <view class="section">
      <view class="section-title">
        <view class="section-number">3</view>
        <text>账号设定</text>
      </view>

      <!-- 目标受众年龄 -->
      <view class="form-item {{errors.target_age_groups ? 'has-error' : ''}}">
        <view class="form-label">
          <text class="required">*</text>
          目标受众年龄（最多选三项）
        </view>
        <view class="checkbox-group">
          <view class="checkbox-item {{ipSetting.target_age_groups.indexOf(item.value) > -1 ? 'checked' : ''}}" wx:for="{{ageGroupOptions}}" wx:key="value" data-value="{{item.value}}" bindtap="toggleAgeGroup">
            <view class="checkbox-icon">
              <van-icon wx:if="{{ipSetting.target_age_groups.indexOf(item.value) > -1}}" name="success" size="18px" color="#fff" />
            </view>
            <text>{{item.label}}</text>
          </view>
        </view>
        <view class="error-message" wx:if="{{errors.target_age_groups}}">{{errors.target_age_groups}}</view>
      </view>

      <!-- 目标受众性别 -->
      <view class="form-item {{errors.target_gender ? 'has-error' : ''}}">
        <view class="form-label">
          <text class="required">*</text>
          目标受众性别
        </view>
        <view class="gender-selector">
          <view class="gender-option {{ipSetting.target_gender === item.value ? 'active' : ''}}" wx:for="{{genderOptions}}" wx:key="value" data-value="{{item.value}}" bindtap="targetGenderSelectEvent">
            <view class="gender-icon" wx:if="{{item.value !== '男女不限'}}">
              <van-icon name="{{item.value === '男' ? 'manager' : 'friends'}}" size="20px" color="{{ipSetting.target_gender === item.value ? '#fff' : '#ff7f11'}}" />
            </view>
            <view class="gender-icon" wx:if="{{item.value === '男女不限'}}">
              <van-icon name="friends-o" size="20px" color="{{ipSetting.target_gender === item.value ? '#fff' : '#ff7f11'}}" />
            </view>
            <text>{{item.label}}</text>
          </view>
        </view>
        <view class="error-message" wx:if="{{errors.target_gender}}">{{errors.target_gender}}</view>
      </view>

      <!-- 目标受众身份或职业 -->
      <view class="form-item {{errors.target_identity ? 'has-error' : ''}}">
        <view class="form-label">
          <text class="required">*</text>
          目标受众身份或职业
        </view>
        <view class="form-textarea">
          <textarea placeholder="请输入目标受众身份或职业" value="{{ipSetting.target_identity}}" data-name="target_identity" bindinput="inputEvent" maxlength="200" show-confirm-bar="{{false}}" />
        </view>
        <view class="error-message" wx:if="{{errors.target_identity}}">{{errors.target_identity}}</view>
      </view>

      <!-- 目标受众兴趣或特征 -->
      <view class="form-item {{errors.target_interests ? 'has-error' : ''}}">
        <view class="form-label">
          <text class="required">*</text>
          目标受众兴趣或特征
        </view>
        <view class="form-textarea">
          <textarea placeholder="请输入目标受众兴趣或特征" value="{{ipSetting.target_interests}}" data-name="target_interests" bindinput="inputEvent" maxlength="200" show-confirm-bar="{{false}}" />
        </view>
        <view class="error-message" wx:if="{{errors.target_interests}}">{{errors.target_interests}}</view>
      </view>

      <!-- 价值主张 -->
      <view class="form-item {{errors.value_proposition ? 'has-error' : ''}}">
        <view class="form-label">
          <text class="required">*</text>
          您希望通过内容，为他们提供什么价值或解决什么问题？
        </view>
        <view class="form-textarea">
          <textarea placeholder="请输入您希望为受众提供的价值" value="{{ipSetting.value_proposition}}" data-name="value_proposition" bindinput="inputEvent" maxlength="300" show-confirm-bar="{{false}}" />
        </view>
        <view class="error-message" wx:if="{{errors.value_proposition}}">{{errors.value_proposition}}</view>
      </view>
    </view>

    <!-- 4. 内容定位 -->
    <view class="section">
      <view class="section-title">
        <view class="section-number">4</view>
        <text>内容定位</text>
      </view>

      <!-- 内容输出方向 -->
      <view class="form-item {{errors.content_direction ? 'has-error' : ''}}">
        <view class="form-label">
          <text class="required">*</text>
          您希望通过账号做哪些方向的内容输出？
        </view>
        <view class="form-textarea">
          <textarea placeholder="请输入内容输出方向" value="{{ipSetting.content_direction}}" data-name="content_direction" bindinput="inputEvent" maxlength="300" show-confirm-bar="{{false}}" />
        </view>
        <view class="error-message" wx:if="{{errors.content_direction}}">{{errors.content_direction}}</view>
      </view>

      <!-- 短视频风格 -->
      <view class="form-item {{errors.content_style ? 'has-error' : ''}}">
        <view class="form-label">
          <text class="required">*</text>
          您希望短视频账号是什么样的风格？
        </view>
        <view class="option-selector">
          <view class="option-item {{ipSetting.content_style === item.value ? 'active' : ''}}" wx:for="{{styleOptions}}" wx:key="value" data-value="{{item.value}}" bindtap="styleSelectEvent">
            <text>{{item.label}}</text>
          </view>
        </view>
        <view class="error-message" wx:if="{{errors.content_style}}">{{errors.content_style}}</view>
      </view>

      <!-- 专属口头禅 -->
      <view class="form-item">
        <view class="form-label">您的专属口头禅？</view>
        <view class="form-input">
          <input type="text" placeholder="请输入您的专属口头禅" value="{{ipSetting.catchphrase}}" data-name="catchphrase" bindinput="inputEvent" maxlength="100" />
        </view>
      </view>
    </view>

  </scroll-view>

  <!-- Bottom placeholder -->
  <view class="bottom-placeholder"></view>

  <!-- Form actions -->
  <view class="form-actions">
    <button class="save-button {{isFormValid ? '' : 'disabled'}}" disabled="{{!isFormValid || isSaving}}" bindtap="saveIPSetting">
      {{isSaving ? '保存中...' : (isEdit ? '更新' : '创建')}}
    </button>
    <view class="cancel-button" bindtap="cancelEdit">取消</view>
  </view>

  <!-- 离开确认弹窗 -->
  <van-dialog use-slot title="确认离开" show="{{ showLeaveConfirm }}" show-cancel-button bind:confirm="confirmLeave" bind:cancel="cancelLeave">
    <view style="padding: 20rpx;">
      您有未保存的更改，确定要离开吗？
    </view>
  </van-dialog>
</view>