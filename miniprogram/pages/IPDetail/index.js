// pages/IPDetail/index.js
const app = getApp();
const {
  PostData,
  GetData
} = require("../../common/server");

// Mock数据
const mockIPData = {
  // 人设定位
  ip_name: "小李老师",
  gender: "女",
  age: 28,
  current_job: "在线教育讲师",
  career_experience: "5年教育行业经验，曾在知名培训机构担任高级讲师，擅长K12数学教学",
  life_experience: "985高校数学系毕业，热爱教育事业，曾获得省级优秀教师称号",
  interests_skills: "数学建模、编程、阅读、瑜伽，擅长将复杂数学概念简化讲解",

  // 商业定位
  products_services: "提供K12数学在线课程、一对一辅导服务、数学思维训练营",
  competitive_advantages: "独创的数学可视化教学法，学生成绩提升率达90%以上，拥有丰富的教学资源库",
  monetization_method: "直接挂车卖课程，同时引流到微信群进行高价值服务转化",

  // 账号设定
  target_age_groups: ["18-24", "25-30", "31-35"],
  target_gender: "男女不限",
  target_identity: "学生家长、在校大学生、数学学习爱好者",
  target_interests: "关注孩子教育、数学学习困难、提升学习成绩",
  value_proposition: "帮助解决数学学习难题，提供高效学习方法，提升数学成绩和思维能力",

  // 内容定位
  content_direction: "数学知识讲解、学习方法分享、教育理念传播、学习技巧演示",
  content_style: "轻松、幽默",
  catchphrase: "数学其实很简单，跟着小李老师一起来探索数学的奥秘吧！",
};
Page({
  /**
   * 页面的初始数据
   */
  data: {
    isEdit: false,
    isSaving: false,
    showLeaveConfirm: false,
    hasChanges: false,
    isFormValid: false,
    errors: {},

    // 目标受众年龄选项
    ageGroupOptions: [{
        value: "18-24",
        label: "18-24岁",
        checked: false
      },
      {
        value: "25-30",
        label: "25-30岁",
        checked: false
      },
      {
        value: "31-35",
        label: "31-35岁",
        checked: false
      },
      {
        value: "36-40",
        label: "36-40岁",
        checked: false
      },
      {
        value: "41-45",
        label: "41-45岁",
        checked: false
      },
      {
        value: "46-50",
        label: "46-50岁",
        checked: false
      },
      {
        value: "50+",
        label: "50岁以上",
        checked: false
      },
    ],

    // 目标受众性别选项
    genderOptions: [{
        value: "男",
        label: "男"
      },
      {
        value: "女",
        label: "女"
      },
      {
        value: "男女不限",
        label: "男女不限"
      },
    ],

    // 内容风格选项
    styleOptions: [{
        value: "轻松、幽默",
        label: "轻松、幽默"
      },
      {
        value: "严肃",
        label: "严肃"
      },
      {
        value: "专业",
        label: "专业"
      },
    ],

    // IP设置数据
    ipSetting: {
      // 人设定位
      ip_name: "",
      gender: "男",
      age: 25,
      current_job: "",
      career_experience: "",
      life_experience: "",
      interests_skills: "",

      // 商业定位
      products_services: "",
      competitive_advantages: "",
      monetization_method: "",

      // 账号设定
      target_age_groups: [{
          value: "18-24",
          label: "18-24岁",
          checked: false
        },
        {
          value: "25-30",
          label: "25-30岁",
          checked: false
        },
        {
          value: "31-35",
          label: "31-35岁",
          checked: false
        },
        {
          value: "36-40",
          label: "36-40岁",
          checked: false
        },
        {
          value: "41-45",
          label: "41-45岁",
          checked: false
        },
        {
          value: "46-50",
          label: "46-50岁",
          checked: false
        },
        {
          value: "50+",
          label: "50岁以上",
          checked: false
        },
      ],
      target_gender: "",
      target_identity: "",
      target_interests: "",
      value_proposition: "",

      // 内容定位
      content_direction: "",
      content_style: "",
      catchphrase: "",
    },

    // 整条数据
    allIpsetting: {}
  },

  /**
   * 输入框事件处理
   */
  inputEvent(e) {
    const key = e.currentTarget.dataset.name;
    const value = e.detail.value;

    this.setData({
      [`ipSetting.${key}`]: value,
      hasChanges: true,
    });

    // 验证表单
    this.validateForm();
  },

  /**
   * 年龄输入事件
   */
  ageInputEvent(e) {
    let value = parseInt(e.detail) || 18;
    if (value < 18) value = 18;
    if (value > 99) value = 99;

    this.setData({
      "ipSetting.age": value,
      hasChanges: true,
    });

    this.validateForm();
  },

  /**
   * 性别选择事件
   */
  genderSelectEvent(e) {
    const value = e.currentTarget.dataset.value;

    this.setData({
      "ipSetting.gender": value,
      hasChanges: true,
    });

    this.validateForm();
  },

  /**
   * 目标受众年龄组选择事件
   */
  toggleAgeGroup(e) {
    const value = e.currentTarget.dataset.value;
    console.log("toggleAgeGroup clicked, value:", value);

    let selectedGroups = [...this.data.ipSetting.target_age_groups];
    console.log("current selectedGroups:", selectedGroups);
    selectedGroups = selectedGroups.map((item) => {
      if (item.value === value) {
        return {
          ...item,
          checked: !item.checked,
        }
      }
      return item
    })
    if (selectedGroups.filter(item => item.checked).length > 3) {
      wx.showToast({
        title: "最多选择3项",
        icon: "none",
      });
      return;
    }


    // if (index > -1) {
    //   selectedGroups.splice(index, 1);
    //   console.log("removed item, new selectedGroups:", selectedGroups);
    // } else {
    // if (selectedGroups.length < 3) {
    //   selectedGroups.push(value);
    //   console.log("added item, new selectedGroups:", selectedGroups);
    // } else {

    // }
    // }

    this.setData({
      "ipSetting.target_age_groups": selectedGroups,
      hasChanges: true,
    });

    console.log(
      "setData completed, current data:",
      this.data.ipSetting.target_age_groups
    );
    this.validateForm();
  },

  /**
   * 目标受众性别选择事件
   */
  targetGenderSelectEvent(e) {
    const value = e.currentTarget.dataset.value;

    this.setData({
      "ipSetting.target_gender": value,
      hasChanges: true,
    });

    this.validateForm();
  },

  /**
   * 内容风格选择事件
   */
  styleSelectEvent(e) {
    const value = e.currentTarget.dataset.value;

    this.setData({
      "ipSetting.content_style": value,
      hasChanges: true,
    });

    this.validateForm();
  },

  /**
   * 清除特定字段的错误
   */
  clearError(e) {
    const key = e.currentTarget.dataset.name;
    const errors = {
      ...this.data.errors,
    };

    if (errors[key]) {
      delete errors[key];
      this.setData({
        errors,
      });
    }
  },

  /**
   * 验证表单
   */
  validateForm() {
    const {
      ipSetting
    } = this.data;
    const errors = {};

    // 人设定位验证
    if (!ipSetting.ip_name || ipSetting.ip_name.trim() === "") {
      errors.ip_name = "请输入IP名称";
    }

    if (!ipSetting.current_job || ipSetting.current_job.trim() === "") {
      errors.current_job = "请输入目前从事的工作";
    }

    // 商业定位验证
    if (
      !ipSetting.products_services ||
      ipSetting.products_services.trim() === ""
    ) {
      errors.products_services = "请输入您的产品或服务";
    }

    if (
      !ipSetting.competitive_advantages ||
      ipSetting.competitive_advantages.trim() === ""
    ) {
      errors.competitive_advantages = "请输入产品或服务的特色优势";
    }

    if (
      !ipSetting.monetization_method ||
      ipSetting.monetization_method.trim() === ""
    ) {
      errors.monetization_method = "请输入变现方式";
    }

    // 账号设定验证
    if (
      !ipSetting.target_age_groups ||
      ipSetting.target_age_groups.filter(item => item.checked).length <= 0
    ) {
      errors.target_age_groups = "请选择目标受众年龄";
    }

    if (!ipSetting.target_gender) {
      errors.target_gender = "请选择目标受众性别";
    }

    if (!ipSetting.target_identity || ipSetting.target_identity.trim() === "") {
      errors.target_identity = "请输入目标受众身份或职业";
    }

    if (
      !ipSetting.target_interests ||
      ipSetting.target_interests.trim() === ""
    ) {
      errors.target_interests = "请输入目标受众兴趣或特征";
    }

    if (
      !ipSetting.value_proposition ||
      ipSetting.value_proposition.trim() === ""
    ) {
      errors.value_proposition = "请输入您希望为受众提供的价值";
    }

    // 内容定位验证
    if (
      !ipSetting.content_direction ||
      ipSetting.content_direction.trim() === ""
    ) {
      errors.content_direction = "请输入内容输出方向";
    }

    if (!ipSetting.content_style) {
      errors.content_style = "请选择短视频风格";
    }

    this.setData({
      errors,
      isFormValid: Object.keys(errors).length === 0,
    });

    return Object.keys(errors).length === 0;
  },
  /**
   * 保存IP设置
   */
  saveIPSetting() {
    // 验证表单
    if (!this.validateForm()) {
      wx.showToast({
        title: "请完善必填信息",
        icon: "none",
      });
      return;
    }
    this.setData({
      isSaving: true,
    });
    const ipSettingData = this.data.allIpsetting;
    if (this.data.isEdit) {
      PostData({
          url: "ipSettingUpdate",
          params: {
            uuid: ipSettingData.uuid,
            open_id: "admin",
            params: this.data.ipSetting
          },
        })
        .then((res) => {
          console.log("res:", res);
          wx.showToast({
            title: this.data.isEdit ? "更新成功" : "创建成功",
            icon: "success",
            duration: 1500,
          });
          wx.navigateBack();
        })
        .finally(() => {
          this.setData({
            isSaving: false,
            hasChanges: false,
          });
        });
    } else {
      PostData({
          url: "ipSettingAdd",
          params: {
            open_id: "admin",
            default: false,
            params: this.data.ipSetting
          },
        })
        .then((res) => {
          console.log("res:", res);
          wx.showToast({
            title: this.data.isEdit ? "更新成功" : "创建成功",
            icon: "success",
            duration: 1500,
          });
          wx.navigateBack();
        })
        .finally(() => {
          this.setData({
            isSaving: false,
            hasChanges: false,
          });
        });
    }
  },

  /**
   * 取消编辑
   */
  cancelEdit() {
    if (this.data.hasChanges) {
      this.setData({
        showLeaveConfirm: true,
      });
    } else {
      wx.navigateBack();
    }
  },

  /**
   * 确认离开
   */
  confirmLeave() {
    this.setData({
      showLeaveConfirm: false,
    });
    wx.navigateBack();
  },

  /**
   * 取消离开
   */
  cancelLeave() {
    this.setData({
      showLeaveConfirm: false,
    });
  },

  /**
   * 加载IP数据
   */
  loadIPData(id) {
    if (!id) return;
    PostData({
      url: "ipSettingDetail",
      params: {
        uuid: id,
      },
    }).then((res) => {
      console.log("res.", res)
      this.setData({
        ipSetting: res.data.params,
        allIpsetting: res.data,
        isEdit: true,
      });

      // 验证表单
      this.validateForm();
    });
    // 模拟加载数据
    // setTimeout(() => {
    //   const ipData = {
    //     id: id,
    //     ip_name: "法律咨询师",
    //     sex: "男",
    //     create_type: "短视频",
    //     audience: "年轻人",
    //     describe: "专业的法律咨询师，擅长民法、刑法等领域",
    //     positioning: "教育",
    //     default: true,
    //   };

    //   this.setData({
    //     ipSetting: ipData,
    //     originalSetting: JSON.parse(JSON.stringify(ipData)),
    //     describeLength: ipData.describe.length,
    //     isEdit: true,
    //   });

    //   // 验证表单
    //   this.validateForm();
    // }, 500);
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options.id) {
      this.loadIPData(options.id);
    } else {
      // 新建模式，加载mock数据用于演示
      if (options.demo === "true") {
        this.setData({
          ipSetting: {
            ...mockIPData
          },
        });
      }
      this.validateForm();
    }
  },

  /**
   * 拦截返回事件
   */
  onBackPress() {
    if (this.data.hasChanges) {
      this.setData({
        showLeaveConfirm: true,
      });
      return true; // 阻止默认返回行为
    }
    return false; // 不阻止默认返回行为
  },
});