/* pages/IPDetail/index.wxss */

/* Global styles */
page {
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  min-height: 100vh;
}

.container {
  min-height: 100vh;
  background: transparent;
  padding-bottom: env(safe-area-inset-bottom);
  display: flex;
  flex-direction: column;
  position: relative;
}

.container::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #ff7f11 0%, #ff5500 50%, #ff7f11 100%);
  opacity: 0.03;
  z-index: -1;
}

/* Header styles */
.form-header {
  background: linear-gradient(135deg, #ff7f11 0%, #ff5500 50%, #ff4500 100%);
  padding: 50rpx 32rpx 40rpx;
  color: #fff;
  box-shadow: 0 8rpx 32rpx rgba(255, 127, 17, 0.25);
  position: relative;
  overflow: hidden;
}

.form-header::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 70%);
  animation: headerGlow 6s ease-in-out infinite;
}

@keyframes headerGlow {

  0%,
  100% {
    transform: rotate(0deg) scale(1);
  }

  50% {
    transform: rotate(180deg) scale(1.1);
  }
}

.header-title {
  font-size: 40rpx;
  font-weight: 700;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.95;
  line-height: 1.5;
  position: relative;
  z-index: 1;
}

/* Form content */
.form-content {
  flex: 1;
  padding: 24rpx 32rpx;
  padding-bottom: 0rpx;
  /* Space for bottom actions */
}

/* Section styles */
.section {
  background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 24rpx;
  padding: 40rpx 36rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #ff7f11, #ff5500, #ff7f11);
  border-radius: 24rpx 24rpx 0 0;
}

.section:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.12),
    0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 36rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f8f9fa;
  position: relative;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -2rpx;
  left: 0;
  width: 80rpx;
  height: 2rpx;
  background: linear-gradient(90deg, #ff7f11, #ff5500);
  border-radius: 1rpx;
}

.section-number {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff7f11 0%, #ff5500 50%, #ff4500 100%);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 700;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 127, 17, 0.3);
  position: relative;
}

.section-number::before {
  content: "";
  position: absolute;
  top: 2rpx;
  left: 2rpx;
  right: 2rpx;
  bottom: 2rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
}

.section-title text {
  font-size: 36rpx;
  font-weight: 700;
  color: #2c3e50;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
}

/* Form item styles */
.form-item {
  margin-bottom: 36rpx;
  position: relative;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-item.has-error .form-input input,
.form-item.has-error .form-textarea textarea {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 4rpx rgba(255, 77, 79, 0.1);
  animation: errorShake 0.5s ease-in-out;
}

@keyframes errorShake {

  0%,
  100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-4rpx);
  }

  75% {
    transform: translateX(4rpx);
  }
}

.form-label {
  font-size: 30rpx;
  color: #2c3e50;
  margin-bottom: 20rpx;
  font-weight: 600;
  display: flex;
  align-items: flex-start;
  line-height: 1.5;
}

.required {
  color: #ff4d4f;
  margin-right: 8rpx;
  font-size: 30rpx;
  font-weight: 700;
}

.form-input {
  position: relative;
}

.form-input input {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  color: #2c3e50;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  box-sizing: border-box;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.form-input input:focus {
  border-color: #ff7f11;
  background: #ffffff;
  box-shadow: 0 0 0 4rpx rgba(255, 127, 17, 0.1),
    0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transform: translateY(-2rpx);
}

.form-input input::placeholder {
  color: #adb5bd;
  font-size: 28rpx;
}

.form-textarea {
  position: relative;
}

.form-textarea textarea {
  width: 100%;
  min-height: 140rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 30rpx;
  color: #2c3e50;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  box-sizing: border-box;
  line-height: 1.6;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  resize: none;
}

.form-textarea textarea:focus {
  border-color: #ff7f11;
  background: #ffffff;
  box-shadow: 0 0 0 4rpx rgba(255, 127, 17, 0.1),
    0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transform: translateY(-2rpx);
}

.form-textarea textarea::placeholder {
  color: #adb5bd;
  font-size: 28rpx;
}

.error-message {
  font-size: 26rpx;
  color: #ff4d4f;
  margin-top: 12rpx;
  line-height: 1.4;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.error-message::before {
  content: "⚠";
  font-size: 24rpx;
}

/* Gender selector styles */
.gender-selector {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
}

.gender-option {
  flex: 1;
  min-width: 140rpx;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 20rpx 28rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 20rpx;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.gender-option::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.4),
      transparent);
  transition: left 0.6s ease;
}

.gender-option:hover {
  border-color: #ff7f11;
  box-shadow: 0 8rpx 24rpx rgba(255, 127, 17, 0.2);
  transform: translateY(-4rpx);
}

.gender-option:hover::before {
  left: 100%;
}

.gender-option.active {
  background: linear-gradient(135deg, #ff7f11 0%, #ff5500 50%, #ff4500 100%);
  border-color: #ff7f11;
  color: #fff;
  box-shadow: 0 12rpx 32rpx rgba(255, 127, 17, 0.4);
  transform: translateY(-6rpx) scale(1.02);
}

.gender-option.active::before {
  background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent);
}

.gender-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36rpx;
  height: 36rpx;
  transition: transform 0.3s ease;
}

.gender-option.active .gender-icon {
  transform: scale(1.1);
}

.gender-option text {
  font-size: 30rpx;
  font-weight: 600;
  color: #2c3e50;
  transition: color 0.3s ease;
}

.gender-option.active text {
  color: #fff;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* Option selector styles */
.option-selector {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 32rpx;
  background-color: #f9f9f9;
  transition: all 0.3s ease;
  cursor: pointer;
}

.option-item.active {
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  border-color: #ff7f11;
  color: #fff;
}

.option-item text {
  font-size: 26rpx;
  color: #333;
}

.option-item.active text {
  color: #fff;
}

/* Checkbox group styles */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 18rpx 28rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 28rpx;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.checkbox-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.4),
      transparent);
  transition: left 0.6s ease;
}

.checkbox-item:hover {
  border-color: #ff7f11;
  box-shadow: 0 8rpx 24rpx rgba(255, 127, 17, 0.2);
  transform: translateY(-4rpx);
}

.checkbox-item:hover::before {
  left: 100%;
}

.checkbox-item.checked {
  background: linear-gradient(135deg, #ff7f11 0%, #ff5500 50%, #ff4500 100%);
  border-color: #ff7f11;
  color: #fff;
  box-shadow: 0 12rpx 32rpx rgba(255, 127, 17, 0.4);
  transform: translateY(-6rpx) scale(1.02);
}

.checkbox-item.checked::before {
  background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent);
}

.checkbox-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 8rpx;
  background-color: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #dee2e6;
  transition: all 0.3s ease;
  position: relative;
}

.checkbox-item.checked .checkbox-icon {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.1);
}

.checkbox-item text {
  font-size: 30rpx;
  font-weight: 600;
  color: #2c3e50;
  transition: color 0.3s ease;
}

.checkbox-item.checked text {
  color: #fff;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* Bottom placeholder */
.bottom-placeholder {
  height: 160rpx;
  width: 100%;
}

/* Form actions */
.form-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 32rpx calc(24rpx + env(safe-area-inset-bottom));
  background: linear-gradient(180deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(255, 255, 255, 0.98) 100%);
  backdrop-filter: blur(20rpx);
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.12),
    0 -2rpx 8rpx rgba(0, 0, 0, 0.04);
  display: flex;
  gap: 20rpx;
  align-items: center;
  z-index: 100;
  border-top: 1rpx solid rgba(255, 255, 255, 0.8);
}

.save-button {
  flex: 1;
  height: 96rpx;
  background: linear-gradient(135deg, #ff7f11 0%, #ff5500 50%, #ff4500 100%);
  color: #fff;
  font-size: 34rpx;
  font-weight: 700;
  border-radius: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(255, 127, 17, 0.3),
    0 4rpx 12rpx rgba(255, 127, 17, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.save-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent);
  transition: left 0.6s ease;
}

.save-button:active::before {
  left: 100%;
}

.save-button:active {
  transform: scale(0.96);
  box-shadow: 0 4rpx 16rpx rgba(255, 127, 17, 0.4),
    0 2rpx 8rpx rgba(255, 127, 17, 0.3);
}

.save-button.disabled {
  background: linear-gradient(135deg, #adb5bd, #6c757d);
  color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  cursor: not-allowed;
}

.save-button.disabled::before {
  display: none;
}

.cancel-button {
  padding: 20rpx 32rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: #6c757d;
  background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2rpx solid #dee2e6;
  border-radius: 32rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.cancel-button:active {
  background: linear-gradient(145deg, #e9ecef 0%, #dee2e6 100%);
  transform: scale(0.96);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.08);
}