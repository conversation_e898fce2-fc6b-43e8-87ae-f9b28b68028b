/* pages/IPDetail/index.wxss */

/* Global styles */
page {
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: env(safe-area-inset-bottom);
  display: flex;
  flex-direction: column;
}

/* Header styles */
.form-header {
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  padding: 40rpx 30rpx;
  color: #fff;
  box-shadow: 0 2rpx 12rpx rgba(255, 127, 17, 0.2);
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 26rpx;
  opacity: 0.9;
  line-height: 1.4;
}

/* Form content */
.form-content {
  flex: 1;
  padding: 20rpx;
  padding-bottom: 160rpx; /* Space for bottom actions */
}

/* Section styles */
.section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-number {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 600;
  margin-right: 16rpx;
}

.section-title text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* Form item styles */
.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-item.has-error .form-input input,
.form-item.has-error .form-textarea textarea {
  border-color: #ff4d4f;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
  display: flex;
  align-items: flex-start;
  line-height: 1.5;
}

.required {
  color: #ff4d4f;
  margin-right: 6rpx;
  font-size: 28rpx;
}

.form-input input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f9f9f9;
  box-sizing: border-box;
}

.form-input input:focus {
  border-color: #ff7f11;
  background-color: #fff;
}

.form-textarea {
  position: relative;
}

.form-textarea textarea {
  width: 100%;
  min-height: 120rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f9f9f9;
  box-sizing: border-box;
  line-height: 1.5;
}

.form-textarea textarea:focus {
  border-color: #ff7f11;
  background-color: #fff;
}

.error-message {
  font-size: 24rpx;
  color: #ff4d4f;
  margin-top: 8rpx;
  line-height: 1.4;
}

/* Option selector styles */
.option-selector {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 32rpx;
  background-color: #f9f9f9;
  transition: all 0.3s ease;
  cursor: pointer;
}

.option-item.active {
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  border-color: #ff7f11;
  color: #fff;
}

.option-item text {
  font-size: 26rpx;
  color: #333;
}

.option-item.active text {
  color: #fff;
}

/* Checkbox group styles */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 24rpx;
  background-color: #f9f9f9;
  transition: all 0.3s ease;
  cursor: pointer;
}

.checkbox-item.checked {
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  border-color: #ff7f11;
  color: #fff;
}

.checkbox-icon {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox-item.checked .checkbox-icon {
  background-color: rgba(255, 255, 255, 0.2);
}

.checkbox-item text {
  font-size: 26rpx;
  color: #333;
}

.checkbox-item.checked text {
  color: #fff;
}

/* Bottom placeholder */
.bottom-placeholder {
  height: 160rpx; /* 底部操作栏的高度加上一些额外空间 */
  width: 100%;
}

/* Form actions */
.form-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx calc(20rpx + env(safe-area-inset-bottom));
  background-color: #fff;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 16rpx;
  align-items: center;
  z-index: 100;
}

.save-button {
  flex: 1;
  height: 88rpx;
  background: linear-gradient(135deg, #ff7f11, #ff5500);
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(255, 127, 17, 0.2);
  transition: all 0.3s ease;
}

.save-button.disabled {
  background: #ccc;
  color: #fff;
  box-shadow: none;
}

.save-button:active {
  transform: scale(0.98);
}

.cancel-button {
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f8f9fa;
  border-radius: 24rpx;
  transition: all 0.3s ease;
}

.cancel-button:active {
  background-color: #e9ecef;
}
