/* pages/IPList/index.wxss */
/* Global container */
.container {
  padding: 0 0 40rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* Header section */
.header {
  padding: 30rpx 30rpx 20rpx;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}




/* Empty state */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 30rpx;
}

.empty-image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 30rpx;
  opacity: 0.7;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.empty-action {
  display: flex;
  align-items: center;
  padding: 20rpx 40rpx;
  background-color: #ff7f11;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.empty-action van-icon {
  margin-left: 10rpx;
}

/* IP List */
.ip-list {
  padding: 20rpx 30rpx;
}

.ip-item-wrapper {
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.ip-item {
  padding: 30rpx;
  background-color: #fff;
  display: flex;
  position: relative;
  transition: all 0.3s ease;
}

.ip-item.managing {
  background-color: #fafafa;
}

.ip-item.default {
  background-color: rgba(255, 127, 17, 0.05);
}

.default-badge {
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(255, 127, 17, 0.1);
  color: #ff7f11;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-bottom-right-radius: 12rpx;
  display: flex;
  align-items: center;
}

.default-badge text {
  margin-left: 4rpx;
}

.ip-content {
  flex: 1;
  padding-right: 20rpx;
}

.ip-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

.ip-description {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ip-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
}

/* Loading state */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-state text {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #999;
}