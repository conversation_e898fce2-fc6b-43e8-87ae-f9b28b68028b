<view class="container">
  <view class="header">
    <view class="header-title">人设列表</view>
  </view>

  <!-- Empty state -->
  <view class="empty-state" wx:if="{{ipList.length === 0}}">
    <image class="empty-image" src="/assets/empty-state.png" mode="aspectFit"></image>
    <view class="empty-text">暂无人设数据</view>
  </view>

  <!-- IP List -->
  <view class="ip-list" wx:else>
    <view class="ip-item-wrapper" wx:for="{{ipList}}" wx:key="id" animation="{{item.animation}}">
      <view class="ip-item {{ manageState ? 'managing' : '' }} {{ item.default ? 'default' : '' }}" bind:tap="switchEvent" data-id="{{item.uuid}}">
        <!-- Default badge -->
        <view class="default-badge" wx:if="{{item.default}}">
          <van-icon name="star" color="#FF7F11" size="24rpx" />
          <text>默认</text>
        </view>

        <view class="ip-content">
          <view class="ip-title">{{item.params.ip_name}}</view>
          <view class="ip-description">{{item.params.career_experience}}</view>
        </view>

        <view class="ip-action">
          <van-icon name="edit" size="40rpx" color="#999" />
        </view>
      </view>
    </view>
  </view>

  <!-- Loading state -->
  <view class="loading-state" wx:if="{{isLoading}}">
    <van-loading color="#FF7F11" />
    <text>加载中...</text>
  </view>
</view>