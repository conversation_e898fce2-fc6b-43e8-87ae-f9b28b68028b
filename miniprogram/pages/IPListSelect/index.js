// pages/IPList/index.js
const app = getApp();
const { PostData } = require("../../common/server");
Page({
  /**
   * 页面的初始数据
   */
  isLoaded: false,
  data: {
    manageState: false,
    isLoading: false,
    ipList: [],
  },
  /** 选择人设 */
  switchEvent(e) {
    const uuid = e.currentTarget.dataset.id;
    const list = this.data.ipList.filter((item) => {
      return uuid === item.uuid;
    });
    const data = wx.getStorageSync("selectIpSetting");
    if (data) {
      data.checked = list[0];
    }
    wx.setStorageSync("selectIpSetting", data);
    wx.navigateBack();
  },
  /**
   * 获取人设列表
   */
  getIPSetting() {
    const _this = this;
    this.setData({
      isLoading: true,
    });
    PostData({
      url: "ipSettingList",
      params: {
        open_id: "admin",
      },
    })
      .then((result) => {
        _this.setData({
          ipList: result.data.data.map((item) => {
            return {
              ...item,
              id: item._id,
            };
          }),
        });
        console.log("result:", result);
      })
      .catch((error) => {
        console.log("error:", error);
      })
      .finally(() => {
        this.setData({
          isLoading: false,
        });
      });
  },

  /**
   * 加载人设数据
   */
  loadIPData() {
    this.setData({
      isLoading: true,
    });

    // 模拟网络请求
    setTimeout(() => {
      this.setData({
        isLoading: false,
      });
    }, 1000);
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.isLoaded = true;
    this.getIPSetting();
  },
  onUnload() {
    this.isLoaded = false;
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次页面显示时刷新数据
    if (this.isLoaded) {
      this.getIPSetting();
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadIPData();

    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },
});
