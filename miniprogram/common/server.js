// const BaseUrl = "http://************"
const BaseUrl = "http://***********";
const WssBaseUrl = "ws://***********";
const WSSBaseUrl = "wss://www.tcai.asia";
const localWSS = "wss://************";
const API = {
  Test: "/ai/test",
  ipSettingAdd: "/ai/ipSetting/add",
  ipSettingList: "/ai/ipSetting/list",
  ipSettingDelete: "/ai/ipSetting/delete",
  ipSettingDetail: "/ai/ipSetting/detail",
  ipSettingUpdate: "/ai/ipSetting/update",
  userInformation: "/ai/user/information",
  uploadVideo: "/ai/createVideoModel",
  getVideoList: "/ai/getVideoModel/list",
  deleteVideo: "/ai/deleteVideoModel",
  uploadAudio:"/ai/createAudioModel",
  

  wssSelectTopic: "/wss/ai/selectTopic",
  wssModify: "/wss/ai/writing/modify",
  wssCreateWriting: "/wss/ai/createWriting",
};
function getUrl(pathname) {
  return `${BaseUrl}${API[pathname]}`;
}
function GetData({ url, params }) {
  console.log("params:", params);
  return new Promise((resolve, reject) => {
    const paths = `${BaseUrl}${API[url]}`;
    wx.request({
      url: paths,
      method: "GET",
      header: {
        "content-type": "application/json",
      },
      data: params || {},
      success(data) {
        console.log("data:", data);
        const result = data.data;
        if (result.code === 200) {
          resolve(result.data);
        } else {
          reject(result);
        }
      },
      fail(err) {
        reject(err);
      },
    });
  });
}

function PostData({ url, params }) {
  return new Promise((resolve, reject) => {
    const paths = `${BaseUrl}${API[url]}`;
    wx.request({
      url: paths,
      method: "POST",
      header: {
        "Content-Type": "application/json",
      },
      data: params,
      success(data) {
        const result = data.data;
        if (result.code !== 200) {
          reject(result);
        } else {
          resolve(result);
        }
      },
      fail(err) {
        console.log(err);
      },
    });
  });
}

function wssData({ url }) {
  // 创建WebSocket连接
  const socketTask = wx.connectSocket({
    url: `${WssBaseUrl}${API[url]}`, //"http://127.0.0.1/wss/ai/selectTopic",
    header: {
      "content-type": "application/json",
      // 'Authorization': 'Bearer your-token' // 如果需要认证
    },
    success: (res) => {
      console.log("WebSocket连接创建成功", res);
    },
    fail: (err) => {
      console.error("WebSocket连接创建失败", err);
      // this.reconnect();
    },
  });
  // 监听事件
  // socketTask.onOpen(() => {
  //   console.log('WebSocket连接已打开');
  //   // this.setData({
  //   //   socketStatus: 'connected'
  //   // });
  //   // this.sendHeartbeat(); // 开始心跳
  // });
  // socketTask.onMessage((res) => {
  //   console.log('收到服务器消息', res);
  //   const message = JSON.parse(res.data);
  //   console.log("message:", message);
  //   // this.handleServerMessage(message);
  // });
  // socketTask.onClose(() => {
  //   console.log('WebSocket连接已关闭');
  //   // this.setData({
  //   //   socketStatus: 'disconnected'
  //   // });
  //   // this.stopHeartbeat();
  //   // this.reconnect();
  // });
  return socketTask;
}
module.exports = {
  GetData,
  PostData,
  wssData,
  getUrl,
};
